import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ChevronDown, Filter, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { departmentGroups } from "@/data/organizationData";

interface OrganizationTableProps {
  selection: {
    businessUnits: string[];
    departmentGroups: string[];
    departments: string[];
    divisions: string[];
    subDivisions: string[];
    categories: string[];
    grades: string[];
    designations: string[];
  };
}

type TableRowData = {
  businessUnit: string;
  departmentGroup: string;
  department: string;
  division: string;
  subDivision: string;
  category: string;
  grade: string;
  designation: string;
  assignees?: number;
};

export function OrganizationTable({ selection }: OrganizationTableProps) {
  // State for filters
  const [filters, setFilters] = useState({
    businessUnit: "",
    departmentGroup: "",
    department: "",
    division: "",
    subDivision: "",
    category: "",
    grade: "",
    designation: "",
    assignees: ""
  });

  // State for filtered rows
  const [filteredRows, setFilteredRows] = useState<TableRowData[]>([]);

  // State to track which filter popover is open
  const [openPopover, setOpenPopover] = useState<string | null>(null);

  // State to track selected values from dropdowns
  const [selectedValues, setSelectedValues] = useState({
    businessUnit: "",
    departmentGroup: "",
    department: "",
    division: "",
    subDivision: "",
    category: "",
    grade: "",
    designation: "",
    assignees: ""
  });

  // State for selected department group
  const [selectedDepartmentGroup, setSelectedDepartmentGroup] = useState("");

  // State to store stable assignee numbers
  const [assigneeNumbers, setAssigneeNumbers] = useState<Record<string, number>>({});

  // Get all unique values for each column from the table rows
  const getUniqueValues = (field: string) => {
    const values = tableRows.map(row => row[field as keyof TableRowData]);
    const uniqueValues = [...new Set(values)].filter(Boolean);
    return uniqueValues as string[];
  };

  // Function to get a stable assignee number for a given combination
  const getAssigneeNumber = (row: Omit<TableRowData, 'assignees'>) => {
    // Create a unique key for this combination
    const key = `${row.businessUnit}-${row.departmentGroup}-${row.department}-${row.division}-${row.subDivision}-${row.category}-${row.grade}-${row.designation}`;

    // If we already have a number for this combination, return it
    if (assigneeNumbers[key]) {
      return assigneeNumbers[key];
    }

    // Otherwise, generate a new random number and store it
    const newNumber = Math.floor(Math.random() * 50) + 10;
    setAssigneeNumbers(prev => ({
      ...prev,
      [key]: newNumber
    }));

    return newNumber;
  };

  // Get all available department groups from all business units
  const getAllDepartmentGroups = () => {
    const allGroups: string[] = [];

    // If business units are selected, get department groups for those units
    if (selection.businessUnits.length > 0) {
      selection.businessUnits.forEach(bu => {
        if (departmentGroups[bu]) {
          allGroups.push(...departmentGroups[bu]);
        }
      });
    } else {
      // If no business units selected, get all department groups
      Object.values(departmentGroups).forEach(groups => {
        allGroups.push(...groups);
      });
    }

    // Return unique department groups
    return [...new Set(allGroups)];
  };

  // Check if there are any selections
  const hasSelections =
    selection.businessUnits.length > 0 ||
    selection.departmentGroups.length > 0 ||
    selection.departments.length > 0 ||
    selection.divisions.length > 0 ||
    selection.subDivisions.length > 0 ||
    selection.categories.length > 0 ||
    selection.grades.length > 0 ||
    selection.designations.length > 0;

  // Even if no selections are made, we'll show sample data

  // Generate table rows based on selections
  const generateTableRows = (): TableRowData[] => {
    const rows: TableRowData[] = [];

    // Sample data to ensure we have divisions displayed
    const sampleData = [
      {
        businessUnit: "KCN",
        departmentGroup: "Clinical Services",
        department: "General Medicine",
        division: "Internal Medicine",
        subDivision: "Cardiology",
        category: "Interventional Cardiology",
        grade: "Junior",
        designation: "Nurse Assistant",
        assignees: 35
      },
      {
        businessUnit: "KCN",
        departmentGroup: "Clinical Services",
        department: "General Medicine",
        division: "Family Medicine",
        subDivision: "Cardiology",
        category: "Interventional Cardiology",
        grade: "Junior",
        designation: "Nurse Assistant",
        assignees: 22
      },
      {
        businessUnit: "KCN",
        departmentGroup: "Clinical Services",
        department: "General Medicine",
        division: "Internal Medicine",
        subDivision: "Cardiology",
        category: "Interventional Cardiology",
        grade: "Junior",
        designation: "Nurse Assistant",
        assignees: 36
      },
      {
        businessUnit: "KCN",
        departmentGroup: "Clinical Services",
        department: "General Medicine",
        division: "Family Medicine",
        subDivision: "Cardiology",
        category: "Interventional Cardiology",
        grade: "Junior",
        designation: "Nurse Assistant",
        assignees: 38
      }
    ];

    // If no selections are made, return sample data
    if (!hasSelections) {
      return sampleData;
    }

    // If no business units are selected, create a default row with a placeholder business unit
    if (selection.businessUnits.length === 0) {
      // Use the current filter's business unit if available, otherwise use a placeholder
      const businessUnit = filters.businessUnit || selectedValues.businessUnit || "All Business Units";

      const rowData = {
        businessUnit: businessUnit,
        departmentGroup: selection.departmentGroups[0] || "",
        department: selection.departments[0] || "",
        division: selection.divisions[0] || "",
        subDivision: selection.subDivisions[0] || "",
        category: selection.categories[0] || "",
        grade: selection.grades[0] || "",
        designation: selection.designations[0] || ""
      };

      rows.push({
        ...rowData,
        assignees: getAssigneeNumber(rowData)
      });
      return rows;
    }

    // For each business unit
    for (const bu of selection.businessUnits) {
      // If no other selections, add a row with just the business unit
      if (
        selection.departmentGroups.length === 0 &&
        selection.departments.length === 0 &&
        selection.divisions.length === 0 &&
        selection.subDivisions.length === 0 &&
        selection.categories.length === 0 &&
        selection.grades.length === 0 &&
        selection.designations.length === 0
      ) {
        const rowData = {
          businessUnit: bu,
          departmentGroup: "Clinical Services", // Default department group
          department: "",
          division: "",
          subDivision: "",
          category: "",
          grade: "",
          designation: ""
        };

        rows.push({
          ...rowData,
          assignees: getAssigneeNumber(rowData)
        });
        continue;
      }

      // For each department group (or an empty one if none selected)
      const deptGroups = selection.departmentGroups.length > 0 ? selection.departmentGroups : [""];
      for (const dg of deptGroups) {
        // For each department (or an empty one if none selected)
        const depts = selection.departments.length > 0 ? selection.departments : [""];
        for (const dept of depts) {
          // For each division (or an empty one if none selected)
          const divs = selection.divisions.length > 0 ? selection.divisions : [""];
          for (const div of divs) {
            // For each sub-division (or an empty one if none selected)
            const subDivs = selection.subDivisions.length > 0 ? selection.subDivisions : [""];
            for (const subDiv of subDivs) {
              // For each category (or an empty one if none selected)
              const cats = selection.categories.length > 0 ? selection.categories : [""];
              for (const cat of cats) {
                // For each grade (or an empty one if none selected)
                const grds = selection.grades.length > 0 ? selection.grades : [""];
                for (const grd of grds) {
                  // For each designation (or an empty one if none selected)
                  const desigs = selection.designations.length > 0 ? selection.designations : [""];
                  for (const desig of desigs) {
                    // Skip if all fields except business unit are empty
                    if (
                      !dg && !dept && !div && !subDiv && !cat && !grd && !desig
                    ) {
                      continue;
                    }

                    const rowData = {
                      businessUnit: bu,
                      departmentGroup: dg,
                      department: dept,
                      division: div,
                      subDivision: subDiv,
                      category: cat,
                      grade: grd,
                      designation: desig
                    };

                    rows.push({
                      ...rowData,
                      assignees: getAssigneeNumber(rowData)
                    });
                  }
                }
              }
            }
          }
        }
      }
    }

    return rows;
  };

  const tableRows = generateTableRows();

  // Reset selected department group when business units change
  useEffect(() => {
    setSelectedDepartmentGroup("");
  }, [selection.businessUnits]);

  // Reset assignee numbers when selection changes
  useEffect(() => {
    // Only reset if there's a change in the selection that would affect the rows
    if (hasSelections) {
      setAssigneeNumbers({});
    }
  }, [
    selection.businessUnits,
    selection.departmentGroups,
    selection.departments,
    selection.divisions,
    selection.subDivisions,
    selection.categories,
    selection.grades,
    selection.designations,
    hasSelections
  ]);

  // Apply filters to rows
  useEffect(() => {
    let result = [...tableRows];

    // Apply each filter - either from dropdown or search input
    if (selectedValues.businessUnit) {
      result = result.filter(row => row.businessUnit === selectedValues.businessUnit);
    } else if (filters.businessUnit) {
      result = result.filter(row =>
        row.businessUnit.toLowerCase().includes(filters.businessUnit.toLowerCase())
      );
    }

    if (selectedValues.departmentGroup) {
      result = result.filter(row => row.departmentGroup === selectedValues.departmentGroup);
    } else if (filters.departmentGroup) {
      result = result.filter(row =>
        row.departmentGroup.toLowerCase().includes(filters.departmentGroup.toLowerCase())
      );
    }

    if (selectedValues.department) {
      result = result.filter(row => row.department === selectedValues.department);
    } else if (filters.department) {
      result = result.filter(row =>
        row.department.toLowerCase().includes(filters.department.toLowerCase())
      );
    }

    if (selectedValues.division) {
      result = result.filter(row => row.division === selectedValues.division);
    } else if (filters.division) {
      result = result.filter(row =>
        row.division.toLowerCase().includes(filters.division.toLowerCase())
      );
    }

    if (selectedValues.subDivision) {
      result = result.filter(row => row.subDivision === selectedValues.subDivision);
    } else if (filters.subDivision) {
      result = result.filter(row =>
        row.subDivision.toLowerCase().includes(filters.subDivision.toLowerCase())
      );
    }

    if (selectedValues.category) {
      result = result.filter(row => row.category === selectedValues.category);
    } else if (filters.category) {
      result = result.filter(row =>
        row.category.toLowerCase().includes(filters.category.toLowerCase())
      );
    }

    if (selectedValues.grade) {
      result = result.filter(row => row.grade === selectedValues.grade);
    } else if (filters.grade) {
      result = result.filter(row =>
        row.grade.toLowerCase().includes(filters.grade.toLowerCase())
      );
    }

    if (selectedValues.designation) {
      result = result.filter(row => row.designation === selectedValues.designation);
    } else if (filters.designation) {
      result = result.filter(row =>
        row.designation.toLowerCase().includes(filters.designation.toLowerCase())
      );
    }

    if (selectedValues.assignees) {
      result = result.filter(row =>
        row.assignees !== undefined &&
        row.assignees.toString() === selectedValues.assignees
      );
    } else if (filters.assignees) {
      result = result.filter(row =>
        row.assignees !== undefined &&
        row.assignees.toString().includes(filters.assignees)
      );
    }

    setFilteredRows(result);
  }, [tableRows, filters, selectedValues]);

  // Function to handle filter changes
  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear dropdown selection when using text filter
    if (value) {
      setSelectedValues(prev => ({
        ...prev,
        [field]: ""
      }));
    }
  };

  // Function to handle dropdown selection
  const handleDropdownSelect = (field: string, value: string) => {
    // If "all" is selected, clear the filter
    if (value === "all") {
      setSelectedValues(prev => ({
        ...prev,
        [field]: ""
      }));

      // Also update selectedDepartmentGroup if the field is departmentGroup
      if (field === 'departmentGroup') {
        setSelectedDepartmentGroup("");
      }
    } else {
      setSelectedValues(prev => ({
        ...prev,
        [field]: value
      }));

      // Also update selectedDepartmentGroup if the field is departmentGroup
      if (field === 'departmentGroup') {
        setSelectedDepartmentGroup(value);
      }
    }

    // Clear the text filter when using dropdown
    setFilters(prev => ({
      ...prev,
      [field]: ""
    }));
  };

  // Function to clear a specific filter
  const clearFilter = (field: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: ""
    }));

    setSelectedValues(prev => ({
      ...prev,
      [field]: ""
    }));

    // Also reset selectedDepartmentGroup if the field is departmentGroup
    if (field === 'departmentGroup') {
      setSelectedDepartmentGroup("");
    }
  };

  // Function to clear all filters
  const clearAllFilters = () => {
    setFilters({
      businessUnit: "",
      departmentGroup: "",
      department: "",
      division: "",
      subDivision: "",
      category: "",
      grade: "",
      designation: "",
      assignees: ""
    });

    setSelectedValues({
      businessUnit: "",
      departmentGroup: "",
      department: "",
      division: "",
      subDivision: "",
      category: "",
      grade: "",
      designation: "",
      assignees: ""
    });

    // Also reset selectedDepartmentGroup
    setSelectedDepartmentGroup("");

    setOpenPopover(null);
  };

  // Group rows by business unit to avoid repetition
  const groupedRows: { [key: string]: TableRowData[] } = {};
  filteredRows.forEach(row => {
    // Ensure business unit is always displayed, even if it's empty
    const businessUnit = row.businessUnit || "N/A";
    if (!groupedRows[businessUnit]) {
      groupedRows[businessUnit] = [];
    }
    groupedRows[businessUnit].push(row);
  });

  return (
    <div className="border rounded-md overflow-hidden">
      <div className="flex justify-end p-2 bg-gray-50 border-b">
        <Button
          variant="outline"
          size="sm"
          onClick={clearAllFilters}
          className="text-xs"
          disabled={!Object.values(filters).some(v => v !== "")}
        >
          Clear All Filters
        </Button>
      </div>
      <Table>
        <TableHeader>
          <TableRow className="bg-primary/10">
            <TableHead className="font-medium sticky left-0 bg-primary/10 z-10">
              <div className="flex items-center justify-between">
                <span>Business Unit</span>
                <Popover open={openPopover === 'businessUnit'} onOpenChange={(open) => setOpenPopover(open ? 'businessUnit' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6">
                      <Filter className={`h-3 w-3 ${filters.businessUnit || selectedValues.businessUnit ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-72 p-3" align="end">
                    <div className="space-y-4">
                      <h4 className="font-medium text-sm">Filter Business Unit</h4>

                      {/* Dropdown filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Select from dropdown</label>
                        <Select
                          value={selectedValues.businessUnit}
                          onValueChange={(value) => handleDropdownSelect('businessUnit', value)}
                        >
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue placeholder="Select business unit" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All business units</SelectItem>
                            {getUniqueValues('businessUnit').map((bu) => (
                              <SelectItem key={bu} value={bu}>{bu}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t"></span>
                        </div>
                        <div className="relative flex justify-center text-xs">
                          <span className="bg-background px-2 text-muted-foreground">OR</span>
                        </div>
                      </div>

                      {/* Text search filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Search by text</label>
                        <Input
                          placeholder="Type to search..."
                          value={filters.businessUnit}
                          onChange={(e) => handleFilterChange('businessUnit', e.target.value)}
                          className="h-8 text-sm"
                        />
                      </div>

                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => clearFilter('businessUnit')}
                          className="text-xs"
                          disabled={!filters.businessUnit && !selectedValues.businessUnit}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>

            <TableHead className="font-medium">
              <div className="flex items-center justify-between">
                <span>Department Groups</span>
                <Popover open={openPopover === 'departmentGroup'} onOpenChange={(open) => setOpenPopover(open ? 'departmentGroup' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6">
                      <Filter className={`h-3 w-3 ${filters.departmentGroup || selectedValues.departmentGroup ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-72 p-3" align="end">
                    <div className="space-y-4">
                      <h4 className="font-medium text-sm">Filter Department Groups</h4>

                      {/* Dropdown filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Select from dropdown</label>
                        <Select
                          value={selectedValues.departmentGroup}
                          onValueChange={(value) => handleDropdownSelect('departmentGroup', value)}
                        >
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue placeholder="Select department group" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All department groups</SelectItem>
                            {getAllDepartmentGroups().map((group) => (
                              <SelectItem key={group} value={group}>{group}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t"></span>
                        </div>
                        <div className="relative flex justify-center text-xs">
                          <span className="bg-background px-2 text-muted-foreground">OR</span>
                        </div>
                      </div>

                      {/* Text search filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Search by text</label>
                        <Input
                          placeholder="Type to search..."
                          value={filters.departmentGroup}
                          onChange={(e) => handleFilterChange('departmentGroup', e.target.value)}
                          className="h-8 text-sm"
                        />
                      </div>

                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => clearFilter('departmentGroup')}
                          className="text-xs"
                          disabled={!filters.departmentGroup && !selectedValues.departmentGroup}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>

            <TableHead className="font-medium">
              <div className="flex items-center justify-between">
                <span>Department</span>
                <Popover open={openPopover === 'department'} onOpenChange={(open) => setOpenPopover(open ? 'department' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6">
                      <Filter className={`h-3 w-3 ${filters.department || selectedValues.department ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-72 p-3" align="end">
                    <div className="space-y-4">
                      <h4 className="font-medium text-sm">Filter Department</h4>

                      {/* Dropdown filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Select from dropdown</label>
                        <Select
                          value={selectedValues.department}
                          onValueChange={(value) => handleDropdownSelect('department', value)}
                        >
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue placeholder="Select department" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All departments</SelectItem>
                            {getUniqueValues('department').map((dept) => (
                              <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t"></span>
                        </div>
                        <div className="relative flex justify-center text-xs">
                          <span className="bg-background px-2 text-muted-foreground">OR</span>
                        </div>
                      </div>

                      {/* Text search filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Search by text</label>
                        <Input
                          placeholder="Type to search..."
                          value={filters.department}
                          onChange={(e) => handleFilterChange('department', e.target.value)}
                          className="h-8 text-sm"
                        />
                      </div>

                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => clearFilter('department')}
                          className="text-xs"
                          disabled={!filters.department && !selectedValues.department}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>

            <TableHead className="font-medium">
              <div className="flex items-center justify-between">
                <span>Division</span>
                <Popover open={openPopover === 'division'} onOpenChange={(open) => setOpenPopover(open ? 'division' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6">
                      <Filter className={`h-3 w-3 ${filters.division || selectedValues.division ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-72 p-3" align="end">
                    <div className="space-y-4">
                      <h4 className="font-medium text-sm">Filter Division</h4>

                      {/* Dropdown filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Select from dropdown</label>
                        <Select
                          value={selectedValues.division}
                          onValueChange={(value) => handleDropdownSelect('division', value)}
                        >
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue placeholder="Select division" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All divisions</SelectItem>
                            {getUniqueValues('division').map((div) => (
                              <SelectItem key={div} value={div}>{div}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t"></span>
                        </div>
                        <div className="relative flex justify-center text-xs">
                          <span className="bg-background px-2 text-muted-foreground">OR</span>
                        </div>
                      </div>

                      {/* Text search filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Search by text</label>
                        <Input
                          placeholder="Type to search..."
                          value={filters.division}
                          onChange={(e) => handleFilterChange('division', e.target.value)}
                          className="h-8 text-sm"
                        />
                      </div>

                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => clearFilter('division')}
                          className="text-xs"
                          disabled={!filters.division && !selectedValues.division}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>

            <TableHead className="font-medium">
              <div className="flex items-center justify-between">
                <span>Sub-division</span>
                <Popover open={openPopover === 'subDivision'} onOpenChange={(open) => setOpenPopover(open ? 'subDivision' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6">
                      <Filter className={`h-3 w-3 ${filters.subDivision || selectedValues.subDivision ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-72 p-3" align="end">
                    <div className="space-y-4">
                      <h4 className="font-medium text-sm">Filter Sub-division</h4>

                      {/* Dropdown filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Select from dropdown</label>
                        <Select
                          value={selectedValues.subDivision}
                          onValueChange={(value) => handleDropdownSelect('subDivision', value)}
                        >
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue placeholder="Select sub-division" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All sub-divisions</SelectItem>
                            {getUniqueValues('subDivision').map((subDiv) => (
                              <SelectItem key={subDiv} value={subDiv}>{subDiv}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t"></span>
                        </div>
                        <div className="relative flex justify-center text-xs">
                          <span className="bg-background px-2 text-muted-foreground">OR</span>
                        </div>
                      </div>

                      {/* Text search filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Search by text</label>
                        <Input
                          placeholder="Type to search..."
                          value={filters.subDivision}
                          onChange={(e) => handleFilterChange('subDivision', e.target.value)}
                          className="h-8 text-sm"
                        />
                      </div>

                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => clearFilter('subDivision')}
                          className="text-xs"
                          disabled={!filters.subDivision && !selectedValues.subDivision}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>

            <TableHead className="font-medium">
              <div className="flex items-center justify-between">
                <span>Category</span>
                <Popover open={openPopover === 'category'} onOpenChange={(open) => setOpenPopover(open ? 'category' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6">
                      <Filter className={`h-3 w-3 ${filters.category || selectedValues.category ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-72 p-3" align="end">
                    <div className="space-y-4">
                      <h4 className="font-medium text-sm">Filter Category</h4>

                      {/* Dropdown filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Select from dropdown</label>
                        <Select
                          value={selectedValues.category}
                          onValueChange={(value) => handleDropdownSelect('category', value)}
                        >
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All categories</SelectItem>
                            {getUniqueValues('category').map((cat) => (
                              <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t"></span>
                        </div>
                        <div className="relative flex justify-center text-xs">
                          <span className="bg-background px-2 text-muted-foreground">OR</span>
                        </div>
                      </div>

                      {/* Text search filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Search by text</label>
                        <Input
                          placeholder="Type to search..."
                          value={filters.category}
                          onChange={(e) => handleFilterChange('category', e.target.value)}
                          className="h-8 text-sm"
                        />
                      </div>

                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => clearFilter('category')}
                          className="text-xs"
                          disabled={!filters.category && !selectedValues.category}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>

            <TableHead className="font-medium">
              <div className="flex items-center justify-between">
                <span>Grade</span>
                <Popover open={openPopover === 'grade'} onOpenChange={(open) => setOpenPopover(open ? 'grade' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6">
                      <Filter className={`h-3 w-3 ${filters.grade || selectedValues.grade ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-72 p-3" align="end">
                    <div className="space-y-4">
                      <h4 className="font-medium text-sm">Filter Grade</h4>

                      {/* Dropdown filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Select from dropdown</label>
                        <Select
                          value={selectedValues.grade}
                          onValueChange={(value) => handleDropdownSelect('grade', value)}
                        >
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue placeholder="Select grade" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All grades</SelectItem>
                            {getUniqueValues('grade').map((grade) => (
                              <SelectItem key={grade} value={grade}>{grade}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t"></span>
                        </div>
                        <div className="relative flex justify-center text-xs">
                          <span className="bg-background px-2 text-muted-foreground">OR</span>
                        </div>
                      </div>

                      {/* Text search filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Search by text</label>
                        <Input
                          placeholder="Type to search..."
                          value={filters.grade}
                          onChange={(e) => handleFilterChange('grade', e.target.value)}
                          className="h-8 text-sm"
                        />
                      </div>

                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => clearFilter('grade')}
                          className="text-xs"
                          disabled={!filters.grade && !selectedValues.grade}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>

            <TableHead className="font-medium">
              <div className="flex items-center justify-between">
                <span>Designation</span>
                <Popover open={openPopover === 'designation'} onOpenChange={(open) => setOpenPopover(open ? 'designation' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6">
                      <Filter className={`h-3 w-3 ${filters.designation || selectedValues.designation ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-72 p-3" align="end">
                    <div className="space-y-4">
                      <h4 className="font-medium text-sm">Filter Designation</h4>

                      {/* Dropdown filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Select from dropdown</label>
                        <Select
                          value={selectedValues.designation}
                          onValueChange={(value) => handleDropdownSelect('designation', value)}
                        >
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue placeholder="Select designation" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All designations</SelectItem>
                            {getUniqueValues('designation').map((desig) => (
                              <SelectItem key={desig} value={desig}>{desig}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t"></span>
                        </div>
                        <div className="relative flex justify-center text-xs">
                          <span className="bg-background px-2 text-muted-foreground">OR</span>
                        </div>
                      </div>

                      {/* Text search filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Search by text</label>
                        <Input
                          placeholder="Type to search..."
                          value={filters.designation}
                          onChange={(e) => handleFilterChange('designation', e.target.value)}
                          className="h-8 text-sm"
                        />
                      </div>

                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => clearFilter('designation')}
                          className="text-xs"
                          disabled={!filters.designation && !selectedValues.designation}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>

            <TableHead className="font-medium text-right">
              <div className="flex items-center justify-end">
                <span># No. of assignees</span>
                <Popover open={openPopover === 'assignees'} onOpenChange={(open) => setOpenPopover(open ? 'assignees' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6 ml-1">
                      <Filter className={`h-3 w-3 ${filters.assignees || selectedValues.assignees ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-72 p-3" align="end">
                    <div className="space-y-4">
                      <h4 className="font-medium text-sm">Filter Assignees</h4>

                      {/* Dropdown filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Select from dropdown</label>
                        <Select
                          value={selectedValues.assignees}
                          onValueChange={(value) => handleDropdownSelect('assignees', value)}
                        >
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue placeholder="Select number" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All values</SelectItem>
                            {getUniqueValues('assignees').map((num) => (
                              <SelectItem key={num} value={num.toString()}>{num}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t"></span>
                        </div>
                        <div className="relative flex justify-center text-xs">
                          <span className="bg-background px-2 text-muted-foreground">OR</span>
                        </div>
                      </div>

                      {/* Text search filter */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Search by text</label>
                        <Input
                          placeholder="Type to search..."
                          value={filters.assignees}
                          onChange={(e) => handleFilterChange('assignees', e.target.value)}
                          className="h-8 text-sm"
                          type="number"
                        />
                      </div>

                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => clearFilter('assignees')}
                          className="text-xs"
                          disabled={!filters.assignees && !selectedValues.assignees}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Object.keys(groupedRows).length > 0 ? (
            Object.entries(groupedRows).map(([businessUnit, rows]) => (
              <React.Fragment key={businessUnit}>
                {rows.map((row, index) => (
                  <TableRow key={`${businessUnit}-${index}`} className="hover:bg-muted/30 group">
                    {/* Always show business unit in every row */}
                    <TableCell className={`sticky left-0 z-10 ${index > 0 ? "border-t-0" : ""} ${row.businessUnit ? "font-medium" : ""} bg-white group-hover:bg-muted/30`}>
                      {businessUnit}
                    </TableCell>
                    <TableCell className={index > 0 ? "border-t-0" : ""}>
                      {row.departmentGroup}
                    </TableCell>
                    <TableCell className={index > 0 ? "border-t-0" : ""}>
                      {row.department}
                    </TableCell>
                    <TableCell className={index > 0 ? "border-t-0" : ""}>
                      {row.division}
                    </TableCell>
                    <TableCell className={index > 0 ? "border-t-0" : ""}>
                      {row.subDivision}
                    </TableCell>
                    <TableCell className={index > 0 ? "border-t-0" : ""}>
                      {row.category}
                    </TableCell>
                    <TableCell className={index > 0 ? "border-t-0" : ""}>
                      {row.grade}
                    </TableCell>
                    <TableCell className={index > 0 ? "border-t-0" : ""}>
                      {row.designation}
                    </TableCell>
                    <TableCell className={`text-right ${index > 0 ? "border-t-0" : ""}`}>
                      {row.assignees}
                    </TableCell>
                  </TableRow>
                ))}
              </React.Fragment>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={9} className="h-24 text-center">
                <div className="flex flex-col items-center justify-center text-muted-foreground">
                  <Filter className="h-8 w-8 mb-2 opacity-40" />
                  <p className="text-sm font-medium">No results found</p>
                  <p className="text-xs mt-1">Try adjusting your filters or clear them to see all data</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearAllFilters}
                    className="mt-4 text-xs"
                  >
                    Clear All Filters
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
