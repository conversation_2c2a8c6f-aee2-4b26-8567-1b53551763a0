import React from "react";
import { NavLink } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  UserCircle,
  Users,
  ClipboardList,
  BookOpen,
  Target,
  Radio,
  Award,
  LineChart,
  GraduationCap,
  FileText,
  ChevronLeft,
  ChevronRight,
  Settings,
  LogOut,
  Layers,
  Briefcase,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";

interface SidebarProps {
  collapsed: boolean;
  setCollapsed: React.Dispatch<React.SetStateAction<boolean>>;
}

const navItems = [
  {
    label: "Dashboard",
    icon: <LayoutDashboard size={20} />,
    href: "/dashboard",
  },
  {
    label: "Curate",
    icon: <BookOpen size={20} />,
    href: "/curate",
  },
  {
    label: "Users",
    icon: <UserCircle size={20} />,
    href: "/users",
  },

  {
    label: "Content Assignment",
    icon: <Briefcase size={20} />,
    href: "/employee-training-assignment",
  },
  {
    label: "Curator Assignment",
    icon: <Target size={20} />,
    href: "/curator-assignment",
  },
  {
    label: "Broadcast",
    icon: <Radio size={20} />,
    href: "/broadcast",
  },
  {
    label: "Certificate",
    icon: <Award size={20} />,
    href: "/certificate",
  },
  {
    label: "Insights",
    icon: <LineChart size={20} />,
    href: "/insights",
  },
  {
    label: "Classroom",
    icon: <GraduationCap size={20} />,
    href: "/classroom",
  },
  {
    label: "Reports",
    icon: <FileText size={20} />,
    href: "/reports",
  },
  {
    label: "H5P Content",
    icon: <Layers size={20} />,
    href: "/h5p-content",
  },
];

export default function Sidebar({ collapsed, setCollapsed }: SidebarProps) {
  const isMobile = useIsMobile();

  return (
    <div
      className={cn(
        "bg-sidebar h-screen flex-shrink-0 transition-all duration-300 overflow-y-auto flex flex-col",
        collapsed ? "w-16" : "w-64"
      )}
    >
      <div className="flex items-center justify-between p-4">
        <div className={cn(
          "flex items-center justify-center rounded-lg p-2",
          "bg-white shadow-sm",
          collapsed ? "w-10 h-10" : "w-auto"
        )}>
          <img
            src="/koach-logo.svg"
            alt="KOACH Learning Xperience Platform"
            className={cn(
              "transition-all duration-300",
              collapsed ? "w-6 h-6" : "w-32 h-auto"
            )}
          />
        </div>
        {!isMobile && (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setCollapsed(!collapsed)}
            className="text-sidebar-foreground hover:bg-sidebar-accent"
          >
            {collapsed ? (
              <ChevronRight size={20} />
            ) : (
              <ChevronLeft size={20} />
            )}
          </Button>
        )}
      </div>
      <nav className="mt-4 space-y-1 px-2 flex-grow">
        {navItems.map((item) => (
          <NavLink
            key={item.href}
            to={item.href}
            className={({ isActive }) =>
              cn("sidebar-item", isActive && "active", {
                "justify-center": collapsed,
              })
            }
          >
            <span>{item.icon}</span>
            {!collapsed && <span>{item.label}</span>}
          </NavLink>
        ))}
      </nav>

      {/* Settings and Logout at bottom */}
      <div className="mt-auto border-t border-sidebar-accent pt-2 px-2 mb-4">
        <NavLink
          to="/settings"
          className={({ isActive }) =>
            cn("sidebar-item", isActive && "active", {
              "justify-center": collapsed,
            })
          }
        >
          <span><Settings size={20} /></span>
          {!collapsed && <span>Settings</span>}
        </NavLink>

        <button
          className={cn("sidebar-item w-full text-left", {
            "justify-center": collapsed,
          })}
          onClick={() => console.log("Logout clicked")}
        >
          <span><LogOut size={20} /></span>
          {!collapsed && <span>Log out</span>}
        </button>
      </div>
    </div>
  );
}
