import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { MultiSelectCheckbox } from "@/components/MultiSelectCheckbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import AssignedContentDropdown from "@/components/AssignedContentDropdown";
import SelectedItemTag from "@/components/SelectedItemTag";
import { Search, Plus, Download, Filter, FilterX, Eye, X, Users, FileDown, Check, ChevronDown } from "lucide-react";
import { Card } from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { motion } from "framer-motion";
import SelectedUnitsWithHierarchy from "@/components/SelectedUnitsWithHierarchy";
import { generateTrainingAssignmentPDF, exportTrainingAssignmentsToExcel } from "@/lib/pdf-utils";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

// Define the TrainingAssignment type
interface TrainingAssignment {
  id: string;
  employeeIds: string[]; // Array of employee IDs
  employeeName?: string; // For backward compatibility
  trainingProgram: string;
  businessUnit: string;
  departmentGroup: string;
  department: string;
  division: string;
  subDivision: string;
  category: string;
  grade: string;
  designation: string;
  status: string;
  assignedDate: string;
  dueDate?: string;
  notes?: string;
}

// Mock data for filters
const businessUnits = ["KCN", "KTN", "KHC", "KVP", "KRR", "KHO", "KHS"];

// Area → Topic → Unit hierarchy for Training
const trainingAreas = ["Clinical", "Administrative", "Technical"];

const trainingTopics = {
  "Clinical": ["Patient Care", "Emergency Medicine", "Pharmacy", "Nursing"],
  "Administrative": ["HR Management", "Finance", "Operations", "Quality Control"],
  "Technical": ["IT Systems", "Medical Equipment", "Facilities", "Data Management"]
};

const trainingUnits = {
  "Patient Care": ["Basic Patient Care", "Advanced Patient Care", "Critical Care"],
  "Emergency Medicine": ["Triage", "Emergency Response", "Trauma Management"],
  "Pharmacy": ["Medication Management", "Pharmacy Operations", "Drug Interactions"],
  "Nursing": ["Basic Nursing", "Advanced Nursing", "Specialized Nursing"],
  "HR Management": ["Recruitment", "Employee Relations", "Performance Management"],
  "Finance": ["Budgeting", "Financial Reporting", "Cost Control"],
  "Operations": ["Workflow Optimization", "Resource Management", "Process Improvement"],
  "Quality Control": ["Quality Assurance", "Compliance", "Risk Management"],
  "IT Systems": ["EHR Systems", "Network Management", "Cybersecurity"],
  "Medical Equipment": ["Equipment Operation", "Maintenance", "Troubleshooting"],
  "Facilities": ["Facility Management", "Safety Protocols", "Environmental Services"],
  "Data Management": ["Data Entry", "Data Analysis", "Reporting"]
};

// Checklist → Form → Document hierarchy for CFD
const cfdAreas = ["Patient Safety Checklist", "Medication Checklist", "Equipment Checklist"];

const cfdTopics = {
  "Patient Safety Checklist": ["Admission Form", "Discharge Form", "Daily Assessment Form"],
  "Medication Checklist": ["Prescription Form", "Administration Form", "Inventory Form"],
  "Equipment Checklist": ["Maintenance Form", "Calibration Form", "Inspection Form"]
};

const cfdUnits = {
  "Admission Form": ["Patient Identification Document", "Medical History Document", "Initial Assessment Document"],
  "Discharge Form": ["Discharge Summary Document", "Follow-up Instructions Document", "Medication List Document"],
  "Daily Assessment Form": ["Vital Signs Document", "Pain Assessment Document", "Progress Notes Document"],
  "Prescription Form": ["Medication Order Document", "Dosage Calculation Document", "Contraindications Document"],
  "Administration Form": ["Administration Schedule Document", "Patient Response Document", "Side Effects Document"],
  "Inventory Form": ["Stock Level Document", "Expiry Tracking Document", "Reorder Document"],
  "Maintenance Form": ["Maintenance Schedule Document", "Service History Document", "Parts Replacement Document"],
  "Calibration Form": ["Calibration Procedure Document", "Accuracy Verification Document", "Calibration Log Document"],
  "Inspection Form": ["Safety Inspection Document", "Functionality Test Document", "Compliance Document"]
};

// State for dynamic hierarchies - will be set in useEffect based on assignment type

// Organizational hierarchy
const departmentGroups = ["Clinical Services", "Emergency Services", "Management", "Quality Assurance", "Information Management", "Infection Prevention", "IT Services", "Patient Services", "Ethics Committee", "Pharmacy Services"];

// Department groups to departments mapping
const departmentsByGroup = {
  "Clinical Services": ["Nursing", "Intensive Care", "Outpatient Services"],
  "Emergency Services": ["Emergency Medicine", "Trauma Care", "Ambulatory Services"],
  "Management": ["Hospital Administration", "Operations Management", "Strategic Planning"],
  "Quality Assurance": ["Quality Control", "Risk Management", "Compliance"],
  "Information Management": ["Health Information", "Medical Records", "Data Analytics"],
  "Infection Prevention": ["Epidemiology", "Infection Control", "Sterilization Services"],
  "IT Services": ["Healthcare IT", "Systems Administration", "Technical Support"],
  "Patient Services": ["Patient Relations", "Patient Advocacy", "Patient Experience"],
  "Ethics Committee": ["Medical Ethics", "Clinical Ethics", "Research Ethics"],
  "Pharmacy Services": ["Pharmacy", "Medication Management", "Pharmaceutical Research"]
};

// Departments to divisions mapping
const divisionsByDepartment = {
  "Nursing": ["Patient Care", "Specialized Nursing", "Nursing Education"],
  "Emergency Medicine": ["Trauma Care", "Emergency Response", "Critical Care"],
  "Hospital Administration": ["Operations", "Strategic Planning", "Resource Management"],
  "Quality Control": ["Patient Safety", "Quality Improvement", "Standards Compliance"],
  "Health Information": ["Records", "Data Management", "Information Systems"],
  "Epidemiology": ["Control Measures", "Outbreak Investigation", "Surveillance"],
  "Healthcare IT": ["Clinical Systems", "Infrastructure", "Applications"],
  "Patient Relations": ["Communication", "Patient Advocacy", "Service Excellence"],
  "Medical Ethics": ["Clinical Ethics", "Research Ethics", "Ethics Education"],
  "Pharmacy": ["Medication Management", "Clinical Pharmacy", "Pharmacy Operations"]
};

// Divisions to sub-divisions mapping
const subDivisionsByDivision = {
  "Patient Care": ["Intensive Care", "General Care", "Specialized Care"],
  "Trauma Care": ["First Response", "Trauma Assessment", "Critical Intervention"],
  "Operations": ["Department Management", "Resource Allocation", "Process Improvement"],
  "Patient Safety": ["Standards Compliance", "Safety Protocols", "Incident Management"],
  "Records": ["Electronic Records", "Records Management", "Documentation"],
  "Control Measures": ["Outbreak Prevention", "Infection Control", "Isolation Protocols"],
  "Clinical Systems": ["EHR Implementation", "Clinical Applications", "System Integration"],
  "Communication": ["Patient Interaction", "Family Communication", "Interdepartmental Communication"],
  "Clinical Ethics": ["Ethical Decision Making", "Ethics Consultation", "Ethics Policy"],
  "Medication Management": ["Dispensing", "Medication Safety", "Medication Reconciliation"]
};

// Sub-divisions to categories mapping
const categoriesBySubDivision = {
  "Intensive Care": ["Clinical", "Critical Care", "Specialized"],
  "First Response": ["Clinical", "Emergency", "Trauma"],
  "Department Management": ["Non-Clinical", "Administrative", "Management"],
  "Standards Compliance": ["Non-Clinical", "Quality", "Regulatory"],
  "Electronic Records": ["Technical", "Information Systems", "Data Management"],
  "Outbreak Prevention": ["Clinical", "Preventive", "Infection Control"],
  "EHR Implementation": ["Technical", "Systems", "Implementation"],
  "Patient Interaction": ["Non-Clinical", "Communication", "Patient Experience"],
  "Ethical Decision Making": ["Clinical", "Ethics", "Decision Support"],
  "Dispensing": ["Clinical", "Pharmacy", "Medication"]
};

// Categories to grades mapping
const gradesByCategory = {
  "Clinical": ["Junior", "Mid-level", "Senior", "Lead"],
  "Non-Clinical": ["Entry-level", "Associate", "Senior", "Manager"],
  "Technical": ["Junior", "Mid-level", "Senior", "Specialist"]
};

// Grades to designations mapping
const designationsByGrade = {
  "Junior": ["Nurse Assistant", "Junior Technician", "Resident"],
  "Mid-level": ["Registered Nurse", "Technician", "Physician"],
  "Senior": ["Head Nurse", "Senior Technician", "Attending Physician"],
  "Lead": ["Nursing Director", "Technical Lead", "Chief Physician"],
  "Entry-level": ["Administrative Assistant", "Records Clerk", "Support Staff"],
  "Associate": ["Administrative Coordinator", "Records Specialist", "Department Coordinator"],
  "Manager": ["Department Manager", "Administrative Manager", "Operations Manager"],
  "Specialist": ["IT Specialist", "Systems Specialist", "Technical Specialist"]
};

const statuses = ["Not Started", "In Progress", "Completed"];

export default function EmployeeTrainingAssignment() {
  const navigate = useNavigate();

  // State for training assignments with sample data
  const [trainingAssignments, setTrainingAssignments] = useState<TrainingAssignment[]>([
    {
      id: "1",
      employeeIds: ["1", "2"],
      employeeName: "2 Employees",
      trainingProgram: "Clinical → Patient Care → Basic Patient Care | Clinical → Nursing → Basic Nursing",
      businessUnit: "KCN, KTN",
      departmentGroup: "Clinical Services, Emergency Services",
      department: "Nursing, Intensive Care",
      division: "Patient Care, Specialized Nursing",
      subDivision: "Intensive Care, General Care",
      category: "Clinical, Critical Care",
      grade: "Junior, Mid-level",
      designation: "Nurse Assistant, Junior Technician",
      status: "Not Started",
      assignedDate: "2024-01-15",
      dueDate: "2024-02-15",
      notes: "Initial training assignment for new hires in clinical services."
    },
    {
      id: "2",
      employeeIds: ["3"],
      employeeName: "Taylor Brown",
      trainingProgram: "Administrative → HR Management → Recruitment",
      businessUnit: "KTN",
      departmentGroup: "Clinical Services",
      department: "General Medicine",
      division: "Internal Medicine",
      subDivision: "Cardiology",
      category: "Interventional Cardiology",
      grade: "Junior",
      designation: "Nurse Assistant",
      status: "In Progress",
      assignedDate: "2024-01-10",
      dueDate: "2024-02-10",
      notes: "Advanced training for experienced staff members."
    },
    {
      id: "3",
      employeeIds: ["e1", "e2"],
      employeeName: "2 Employees",
      trainingProgram: "Technical → IT Systems → EHR Systems | Technical → Medical Equipment → Equipment Operation",
      businessUnit: "KCN",
      departmentGroup: "Clinical Services",
      department: "Surgery",
      division: "Internal Medicine",
      subDivision: "Cardiology",
      category: "Interventional Cardiology",
      grade: "Junior",
      designation: "Junior Technician",
      status: "Completed",
      assignedDate: "2024-01-05",
      dueDate: "2024-01-20",
      notes: "Completed technical training for IT systems and medical equipment."
    }
  ]);

  // State for training program details dialog
  const [programDetailsOpen, setProgramDetailsOpen] = useState(false);
  const [selectedProgram, setSelectedProgram] = useState<string>("");

  // State for assignment details dialog
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedAssignment, setSelectedAssignment] = useState<TrainingAssignment | null>(null);

  // State for edit dialog
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [assignmentToEdit, setAssignmentToEdit] = useState<TrainingAssignment | null>(null);

  // State for expanded rows in accordion
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  // Function to toggle row expansion
  const toggleRowExpansion = (assignmentId: string) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(assignmentId)) {
      newExpandedRows.delete(assignmentId);
    } else {
      newExpandedRows.add(assignmentId);
    }
    setExpandedRows(newExpandedRows);
  };

  const [searchTerm, setSearchTerm] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentStep, setCurrentStep] = useState(1);
  const [assignmentType, setAssignmentType] = useState<"Training" | "CFD">("Training");
  const [filters, setFilters] = useState({
    businessUnit: [] as string[],
    departmentGroup: [] as string[],
    department: [] as string[],
    division: [] as string[],
    subDivision: [] as string[],
    category: [] as string[],
    grade: [] as string[],
    designation: [] as string[],
    status: [] as string[],
  });

  // State to track which filter dropdown is open
  const [openFilterDropdown, setOpenFilterDropdown] = useState<string | null>(null);

  // State for dialog form fields
  const [notes, setNotes] = useState("");
  const [selectedEmployeeIds, setSelectedEmployeeIds] = useState<string[]>([]);

  // Mock users data (in a real app, this would be fetched from an API)
  const [users, setUsers] = useState([
    // Internal users
    {
      id: "1",
      name: "Alex Johnson",
      employeeNo: "EMP-001",
      email: "<EMAIL>",
      department: "Engineering",
      division: "Technology",
      type: "internal"
    },
    {
      id: "2",
      name: "Jamie Smith",
      employeeNo: "EMP-002",
      email: "<EMAIL>",
      department: "Finance",
      division: "Business",
      type: "internal"
    },
    {
      id: "3",
      name: "Taylor Brown",
      employeeNo: "EMP-003",
      email: "<EMAIL>",
      department: "Product",
      division: "Technology",
      type: "internal"
    },
    // External users
    {
      id: "e1",
      name: "Robin Lee",
      employeeNo: "EXT-001",
      email: "<EMAIL>",
      department: "Research",
      division: "Healthcare",
      type: "external"
    },
    {
      id: "e2",
      name: "Sam Green",
      employeeNo: "EXT-002",
      email: "<EMAIL>",
      department: "Training",
      division: "Education",
      type: "external"
    }
  ]);

  // Get the selected employees
  const selectedEmployees = selectedEmployeeIds.length > 0
    ? users.filter(user => selectedEmployeeIds.includes(user.id))
    : [];

  // Initialize any existing assignments with employeeIds if they don't have it
  useEffect(() => {
    setTrainingAssignments(prevAssignments =>
      prevAssignments.map(assignment => {
        if (!assignment.employeeIds) {
          return {
            ...assignment,
            employeeIds: [users.find(u => u.name === assignment.employeeName)?.id || "1"]
          };
        }
        return assignment;
      })
    );
  }, [users]);

  // State for dynamic hierarchies
  const [areas, setAreas] = useState<string[]>(trainingAreas);
  const [topics, setTopics] = useState<Record<string, string[]>>(trainingTopics);
  const [units, setUnits] = useState<Record<string, string[]>>(trainingUnits);

  // Effect for assignment type change
  useEffect(() => {
    // Reset selections when assignment type changes
    setSelectedArea("");
    setSelectedTopic("");
    setSelectedUnits([]);
    setAvailableTopics([]);
    setAvailableUnits([]);
    setAllUnitsSelected(false);
    setProgramSearchTerm(""); // Reset program search term

    // Update the hierarchies based on assignment type
    if (assignmentType === "Training") {
      setAreas(trainingAreas);
      setTopics(trainingTopics);
      setUnits(trainingUnits);
      // Set all training areas as expanded by default
      setExpandedAreas([...trainingAreas]);
    } else {
      setAreas(cfdAreas);
      setTopics(cfdTopics);
      setUnits(cfdUnits);
      // Set all CFD areas as expanded by default
      setExpandedAreas([...cfdAreas]);
    }
  }, [assignmentType]);

  // Area → Topic → Unit hierarchy state
  const [selectedArea, setSelectedArea] = useState("");
  const [selectedTopic, setSelectedTopic] = useState("");
  const [selectedUnits, setSelectedUnits] = useState<string[]>([]);
  const [selectedTopics, setSelectedTopics] = useState<{area: string, topic: string}[]>([]);
  const [selectedCfdUnits, setSelectedCfdUnits] = useState<{area: string, topic: string, unit: string}[]>([]);
  const [availableTopics, setAvailableTopics] = useState<string[]>([]);
  const [availableUnits, setAvailableUnits] = useState<string[]>([]);
  const [allUnitsSelected, setAllUnitsSelected] = useState(false);
  const [programSearchTerm, setProgramSearchTerm] = useState("");
  // State to track expanded areas in the accordion - initialize with all areas expanded
  const [expandedAreas, setExpandedAreas] = useState<string[]>(
    assignmentType === "Training" ? trainingAreas : cfdAreas
  );

  // Function to get all units from all areas and topics based on assignment type
  const getAllUnits = () => {
    const allUnits: string[] = [];
    const currentUnits = assignmentType === "Training" ? trainingUnits : cfdUnits;

    Object.values(currentUnits).forEach(unitArray => {
      unitArray.forEach(unit => {
        if (!allUnits.includes(unit)) {
          allUnits.push(unit);
        }
      });
    });
    return allUnits;
  };

  // Function to filter areas, topics, and units based on search term
  const filterProgramItems = (searchTerm: string) => {
    if (!searchTerm.trim()) {
      return {
        filteredAreas: assignmentType === "Training" ? trainingAreas : cfdAreas,
        filteredTopics: assignmentType === "Training" ? trainingTopics : cfdTopics,
        filteredUnits: assignmentType === "Training" ? trainingUnits : cfdUnits,
        matchCounts: { areas: 0, topics: 0, units: 0 }
      };
    }

    const currentAreas = assignmentType === "Training" ? trainingAreas : cfdAreas;
    const currentTopics = assignmentType === "Training" ? trainingTopics : cfdTopics;
    const currentUnits = assignmentType === "Training" ? trainingUnits : cfdUnits;

    const searchTermLower = searchTerm.toLowerCase();

    // Find matching areas
    const matchingAreas = currentAreas.filter(area =>
      area.toLowerCase().includes(searchTermLower)
    );

    // Find matching topics
    const matchingTopicsByArea: Record<string, string[]> = {};
    const allMatchingTopics: string[] = [];

    Object.entries(currentTopics).forEach(([area, topicList]) => {
      const matchingTopics = topicList.filter(topic =>
        topic.toLowerCase().includes(searchTermLower)
      );

      if (matchingTopics.length > 0 || matchingAreas.includes(area)) {
        matchingTopicsByArea[area] = matchingTopics.length > 0 ? matchingTopics : topicList;
        matchingTopics.forEach(topic => {
          if (!allMatchingTopics.includes(topic)) {
            allMatchingTopics.push(topic);
          }
        });
      }
    });

    // Find matching units
    const matchingUnitsByTopic: Record<string, string[]> = {};
    const allMatchingUnits: string[] = [];

    Object.entries(currentUnits).forEach(([topic, unitList]) => {
      const matchingUnits = unitList.filter(unit =>
        unit.toLowerCase().includes(searchTermLower)
      );

      if (matchingUnits.length > 0 || allMatchingTopics.includes(topic)) {
        matchingUnitsByTopic[topic] = matchingUnits.length > 0 ? matchingUnits : unitList;
        matchingUnits.forEach(unit => {
          if (!allMatchingUnits.includes(unit)) {
            allMatchingUnits.push(unit);
          }
        });
      }
    });

    // Add areas that have matching topics or units
    const filteredAreas = currentAreas.filter(area => {
      // Include if area matches search
      if (matchingAreas.includes(area)) return true;

      // Include if any of its topics match search
      const areaTopics = currentTopics[area] || [];
      if (areaTopics.some(topic => allMatchingTopics.includes(topic))) return true;

      // Include if any of its topics have matching units
      return areaTopics.some(topic => {
        const topicUnits = currentUnits[topic] || [];
        return topicUnits.some(unit => allMatchingUnits.includes(unit));
      });
    });

    // Create filtered topics object
    const filteredTopics: Record<string, string[]> = {};
    filteredAreas.forEach(area => {
      const areaTopics = currentTopics[area] || [];
      filteredTopics[area] = areaTopics.filter(topic => {
        // Include if topic matches search
        if (allMatchingTopics.includes(topic)) return true;

        // Include if any of its units match search
        const topicUnits = currentUnits[topic] || [];
        return topicUnits.some(unit => allMatchingUnits.includes(unit));
      });
    });

    // Create filtered units object
    const filteredUnits: Record<string, string[]> = {};
    Object.keys(filteredTopics).forEach(area => {
      filteredTopics[area].forEach(topic => {
        const topicUnits = currentUnits[topic] || [];
        filteredUnits[topic] = topicUnits.filter(unit => {
          // Include if unit matches search or if we're showing all units for this topic
          return allMatchingUnits.includes(unit) || allMatchingTopics.includes(topic);
        });
      });
    });

    return {
      filteredAreas,
      filteredTopics,
      filteredUnits,
      matchCounts: {
        areas: matchingAreas.length,
        topics: allMatchingTopics.length,
        units: allMatchingUnits.length
      }
    };
  };



  // Business Unit multi-select state
  const [selectedBusinessUnits, setSelectedBusinessUnits] = useState<string[]>([]);

  // Organizational hierarchy state
  const [selectedDepartmentGroups, setSelectedDepartmentGroups] = useState<string[]>([]);
  const [selectedDepartments, setSelectedDepartments] = useState<string[]>([]);
  const [selectedDivisions, setSelectedDivisions] = useState<string[]>([]);
  const [selectedSubDivisions, setSelectedSubDivisions] = useState<string[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedGrades, setSelectedGrades] = useState<string[]>([]);
  const [selectedDesignations, setSelectedDesignations] = useState<string[]>([]);

  // Available options based on selections
  const [availableDepartments, setAvailableDepartments] = useState<string[]>([]);
  const [availableDivisions, setAvailableDivisions] = useState<string[]>([]);
  const [availableSubDivisions, setAvailableSubDivisions] = useState<string[]>([]);
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const [availableGrades, setAvailableGrades] = useState<string[]>([]);
  const [availableDesignations, setAvailableDesignations] = useState<string[]>([]);

  // We'll use direct preview in the UI instead of a separate state

  // Effect for Area → Topic → Unit hierarchy
  useEffect(() => {
    if (selectedArea) {
      const currentTopics = assignmentType === "Training" ? trainingTopics : cfdTopics;
      setAvailableTopics(currentTopics[selectedArea as keyof typeof currentTopics] || []);
      setSelectedTopic("");
      setSelectedUnits([]);
      setAvailableUnits([]);
    } else {
      setAvailableTopics([]);
    }
  }, [selectedArea, assignmentType]);

  useEffect(() => {
    if (selectedTopic) {
      const currentUnits = assignmentType === "Training" ? trainingUnits : cfdUnits;
      setAvailableUnits(currentUnits[selectedTopic as keyof typeof currentUnits] || []);
      setSelectedUnits([]);
    } else {
      setAvailableUnits([]);
    }
  }, [selectedTopic, assignmentType]);

  // Effect to clear Department Groups when no Business Units are selected
  useEffect(() => {
    if (selectedBusinessUnits.length === 0) {
      // Clear Department Groups when no Business Units are selected
      setSelectedDepartmentGroups([]);
    }
  }, [selectedBusinessUnits]);

  // Effects for organizational hierarchy
  useEffect(() => {
    if (selectedDepartmentGroups.length > 0) {
      const allDepartments = selectedDepartmentGroups.flatMap(
        group => departmentsByGroup[group as keyof typeof departmentsByGroup] || []
      );
      setAvailableDepartments([...new Set(allDepartments)]);
      setSelectedDepartments([]);
      setSelectedDivisions([]);
      setSelectedSubDivisions([]);
      setSelectedCategories([]);
      setSelectedGrades([]);
      setSelectedDesignations([]);
    } else {
      setAvailableDepartments([]);
    }
  }, [selectedDepartmentGroups]);

  useEffect(() => {
    if (selectedDepartments.length > 0) {
      // Get all divisions for all selected departments
      const allDivisions = selectedDepartments.flatMap(
        dept => divisionsByDepartment[dept as keyof typeof divisionsByDepartment] || []
      );
      setAvailableDivisions([...new Set(allDivisions)]);
      setSelectedDivisions([]);
      setSelectedSubDivisions([]);
      setSelectedCategories([]);
      setSelectedGrades([]);
      setSelectedDesignations([]);
    } else {
      setAvailableDivisions([]);
    }
  }, [selectedDepartments]);

  useEffect(() => {
    if (selectedDivisions.length > 0) {
      // Get all sub-divisions for all selected divisions
      const allSubDivisions = selectedDivisions.flatMap(
        div => subDivisionsByDivision[div as keyof typeof subDivisionsByDivision] || []
      );
      setAvailableSubDivisions([...new Set(allSubDivisions)]);
      setSelectedSubDivisions([]);
      setSelectedCategories([]);
      setSelectedGrades([]);
      setSelectedDesignations([]);
    } else {
      setAvailableSubDivisions([]);
    }
  }, [selectedDivisions]);

  useEffect(() => {
    if (selectedSubDivisions.length > 0) {
      // Get all categories for all selected sub-divisions
      const allCategories = selectedSubDivisions.flatMap(
        subDiv => categoriesBySubDivision[subDiv as keyof typeof categoriesBySubDivision] || []
      );
      setAvailableCategories([...new Set(allCategories)]);
      setSelectedCategories([]);
      setSelectedGrades([]);
      setSelectedDesignations([]);
    } else {
      setAvailableCategories([]);
    }
  }, [selectedSubDivisions]);

  useEffect(() => {
    if (selectedCategories.length > 0) {
      // Get all grades for all selected categories
      const allGrades = selectedCategories.flatMap(
        cat => gradesByCategory[cat as keyof typeof gradesByCategory] || []
      );
      setAvailableGrades([...new Set(allGrades)]);
      setSelectedGrades([]);
      setSelectedDesignations([]);
    } else {
      setAvailableGrades([]);
    }
  }, [selectedCategories]);

  useEffect(() => {
    if (selectedGrades.length > 0) {
      // Get all designations for all selected grades
      const allDesignations = selectedGrades.flatMap(
        grade => designationsByGrade[grade as keyof typeof designationsByGrade] || []
      );
      setAvailableDesignations([...new Set(allDesignations)]);
      setSelectedDesignations([]);
    } else {
      setAvailableDesignations([]);
    }
  }, [selectedGrades]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Filter assignments based on search term and filters
  const filteredAssignments = trainingAssignments.filter((assignment: TrainingAssignment) => {
    const matchesSearch =
      assignment.trainingProgram.toLowerCase().includes(searchTerm.toLowerCase());

    // Check if the assignment matches each filter
    // If filter array is empty, return true (no filter applied)
    // Otherwise, check if the assignment value is in the filter array
    const matchesBusinessUnit = filters.businessUnit.length === 0 ||
      filters.businessUnit.some(filter => assignment.businessUnit.includes(filter));

    const matchesDepartmentGroup = filters.departmentGroup.length === 0 ||
      filters.departmentGroup.some(filter => assignment.departmentGroup.includes(filter));

    const matchesDepartment = filters.department.length === 0 ||
      filters.department.includes(assignment.department);

    const matchesDivision = filters.division.length === 0 ||
      filters.division.includes(assignment.division);

    const matchesSubDivision = filters.subDivision.length === 0 ||
      filters.subDivision.includes(assignment.subDivision);

    const matchesCategory = filters.category.length === 0 ||
      filters.category.includes(assignment.category);

    const matchesGrade = filters.grade.length === 0 ||
      filters.grade.includes(assignment.grade);

    const matchesDesignation = filters.designation.length === 0 ||
      filters.designation.includes(assignment.designation);

    const matchesStatus = filters.status.length === 0 ||
      filters.status.includes(assignment.status);

    return matchesSearch && matchesBusinessUnit && matchesDepartmentGroup && matchesDepartment &&
           matchesDivision && matchesSubDivision && matchesCategory && matchesGrade &&
           matchesDesignation && matchesStatus;
  });

  // Calculate pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredAssignments.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredAssignments.length / itemsPerPage);

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => {
      // If value is "all", clear the filter
      if (value === "all") {
        return {
          ...prev,
          [key]: []
        };
      }

      // Check if the value is already in the array
      const currentValues = prev[key as keyof typeof prev] as string[];
      if (currentValues.includes(value)) {
        // Remove the value if it's already selected
        return {
          ...prev,
          [key]: currentValues.filter(v => v !== value)
        };
      } else {
        // Add the value if it's not already selected
        return {
          ...prev,
          [key]: [...currentValues, value]
        };
      }
    });
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Function to check if a filter value is selected
  const isFilterSelected = (key: string, value: string): boolean => {
    const filterValues = filters[key as keyof typeof filters] as string[];
    return filterValues.includes(value);
  };

  const clearFilters = () => {
    setFilters({
      businessUnit: [],
      departmentGroup: [],
      department: [],
      division: [],
      subDivision: [],
      category: [],
      grade: [],
      designation: [],
      status: [],
    });
    setSearchTerm("");
    setCurrentPage(1);
  };

  // Count total active filters
  const activeFilterCount = Object.values(filters).reduce(
    (count, filterValues) => count + (filterValues as string[]).length,
    0
  );

  const handleAssignTraining = () => {
    // Validate required fields
    if ((assignmentType === "Training" ?
          (selectedTopics.length === 0 && !allUnitsSelected) :
          (selectedCfdUnits.length === 0 && !allUnitsSelected)) ||
        selectedBusinessUnits.length === 0) {
      toast({
        title: "Missing Information",
        description: assignmentType === "Training"
          ? "Please select at least one topic and at least one business unit."
          : "Please select at least one document and at least one business unit.",
        variant: "destructive",
      });
      return;
    }

    // Validate organizational hierarchy fields
    if (selectedDepartmentGroups.length === 0 || selectedDepartments.length === 0 || selectedDivisions.length === 0 ||
        selectedSubDivisions.length === 0 || selectedCategories.length === 0 || selectedGrades.length === 0 ||
        selectedDesignations.length === 0) {
      toast({
        title: "Missing Information",
        description: "Please complete all organizational hierarchy selections.",
        variant: "destructive",
      });
      return;
    }

    // Validate that at least one employee is selected
    if (selectedEmployeeIds.length === 0) {
      toast({
        title: "Missing Information",
        description: "Please select at least one employee.",
        variant: "destructive",
      });
      return;
    }

    // Create a single training assignment for all selected employees
    const newAssignment = {
      id: `${trainingAssignments.length + 1}`,
      employeeIds: selectedEmployeeIds, // Store all employee IDs
      employeeName: selectedEmployeeIds.length === 1
        ? users.find(user => user.id === selectedEmployeeIds[0])?.name || "Unknown"
        : `${selectedEmployeeIds.length} Employees`, // For display purposes
      // Create a training program string that includes all selected units
      trainingProgram: allUnitsSelected
        ? assignmentType === "Training" ? "All Units Selected" : "All Documents Selected"
        : assignmentType === "Training"
          ? selectedTopics.map(({ area, topic }) => {
              return `${area} -> ${topic}`;
            }).join(" | ")
          : selectedCfdUnits.map(({ area, topic, unit }) => {
              return `${area} -> ${topic} -> ${unit}`;
            }).join(" | "),
      businessUnit: selectedBusinessUnits.join(", "),
      departmentGroup: selectedDepartmentGroups.join(", "),
      department: selectedDepartments.join(", "),
      division: selectedDivisions.join(", "),
      subDivision: selectedSubDivisions.join(", "),
      category: selectedCategories.join(", "),
      grade: selectedGrades.join(", "),
      designation: selectedDesignations.join(", "),
      status: "Not Started",
      assignedDate: new Date().toISOString().split('T')[0],
      notes: notes
    };

    // Add the new assignment to the state
    setTrainingAssignments([...trainingAssignments, newAssignment]);

    // Reset form fields
    setSelectedArea("");
    setSelectedTopic("");
    setSelectedUnits([]);
    setSelectedTopics([]);
    setSelectedCfdUnits([]);
    setAllUnitsSelected(false);
    setSelectedBusinessUnits([]);
    setSelectedDepartmentGroups([]);
    setSelectedDepartments([]);
    setSelectedDivisions([]);
    setSelectedSubDivisions([]);
    setSelectedCategories([]);
    setSelectedGrades([]);
    setSelectedDesignations([]);
    setSelectedEmployeeIds([]);
    setNotes("");

    // Close dialog
    setDialogOpen(false);

    toast({
      title: `${assignmentType} Assigned`,
      description: `${assignmentType} has been assigned to ${selectedEmployeeIds.length} employee(s) successfully.`,
    });
  };

  // Reset form when dialog is closed
  const handleDialogOpenChange = (open: boolean) => {
    if (!open) {
      // Reset form fields when dialog is closed
      setSelectedArea("");
      setSelectedTopic("");
      setSelectedUnits([]);
      setSelectedTopics([]);
      setSelectedCfdUnits([]);
      setAllUnitsSelected(false);
      setSelectedBusinessUnits([]);
      setSelectedDepartmentGroups([]);
      setSelectedDepartments([]);
      setSelectedDivisions([]);
      setSelectedSubDivisions([]);
      setSelectedCategories([]);
      setSelectedGrades([]);
      setSelectedDesignations([]);
      setSelectedEmployeeIds([]);
      setNotes("");
      setProgramSearchTerm(""); // Reset program search term
      setAssignmentType("Training"); // Reset assignment type to default
    }
    setDialogOpen(open);
  };

  return (
    <>
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        <h1 className="text-3xl font-bold tracking-tight mb-2">Employee Training & CFD Assignment</h1>
        <p className="text-muted-foreground">Assign and manage training programs and CFDs for employees</p>

        {/* Selected Business Units at the top of the page */}
        {filters.businessUnit.length > 0 && (
          <div className="mt-4">
            <div className="border rounded-md bg-white">
              {/* Header without background */}
              <div className="px-3 py-2">
                <div className="flex items-center justify-between w-full">
                  <h4 className="text-sm font-medium">
                    Selected Business Units
                    <span className="text-xs text-muted-foreground ml-2">
                      {filters.businessUnit.length} selected
                    </span>
                  </h4>
                </div>
              </div>
              {/* Content */}
              <div className="px-3 pt-0 pb-3">
                <div className="flex flex-wrap gap-1 mt-2">
                  {filters.businessUnit.map((unit) => (
                    <SelectedItemTag
                      key={unit}
                      label={unit}
                      onRemove={() => handleFilterChange("businessUnit", unit)}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </motion.div>

      <motion.div variants={itemVariants}>
        <Card className="p-4 md:p-6 shadow-md border-muted/40">
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0 mb-6">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search by training program..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 transition-all duration-200 focus:ring-2 focus:ring-primary/20"
              />
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                className="gap-2 transition-all duration-200 hover:bg-muted/80 hover:border-primary/30"
                onClick={clearFilters}
              >
                <FilterX size={16} />
                Clear Filters
              </Button>
              <Button
                variant="outline"
                className="gap-2 transition-all duration-200 hover:bg-muted/80 hover:border-primary/30"
                onClick={() => {
                  try {
                    // Export to Excel
                    exportTrainingAssignmentsToExcel(filteredAssignments);

                    toast({
                      title: "Excel Generated",
                      description: "Training assignments have been exported to Excel.",
                    });
                  } catch (error) {
                    console.error("Error exporting to Excel:", error);
                    toast({
                      title: "Error",
                      description: "There was an error exporting to Excel. Please try again.",
                      variant: "destructive",
                    });
                  }
                }}
              >
                <Download size={16} />
                Export
              </Button>
              <div className="flex gap-2">
                <Button
                  className="gap-2 transition-all duration-200 hover:bg-primary/90 shadow-sm"
                  onClick={() => {
                    setAssignmentType("Training");
                    setDialogOpen(true);
                  }}
                >
                  <Plus size={16} />
                  Assign Training
                </Button>
                <Button
                  className="gap-2 transition-all duration-200 hover:bg-primary/90 shadow-sm"
                  onClick={() => {
                    setAssignmentType("CFD");
                    setDialogOpen(true);
                  }}
                >
                  <Plus size={16} />
                  Assign CFD
                </Button>
              </div>
              <Dialog open={dialogOpen} onOpenChange={handleDialogOpenChange}>
                <DialogContent className="sm:max-w-[1200px] h-[90vh] p-0 flex flex-col overflow-hidden border-muted/40 shadow-lg">
                  <DialogHeader className="px-6 pt-6 pb-2 bg-muted/10">
                    <DialogTitle className="text-xl font-semibold">
                      Knowledge Assignment
                    </DialogTitle>
                    <DialogDescription className="text-muted-foreground">
                      Assign learning content to user based on their role and responsibilities with in the organization.
                    </DialogDescription>
                  </DialogHeader>

                  <div className="flex flex-1 overflow-hidden">
                    {/* Main Content - Left Side */}
                    <ScrollArea className="flex-1 overflow-auto px-6">
                      <div className="grid gap-3 py-4">

                        {/* Step 1: Training Program or CFD Selection */}
                        {currentStep === 1 && (
                          <div className="grid grid-cols-1 gap-4">
                            <h3 className="text-md font-semibold">
                              {assignmentType === "Training" ? "Training Program" : "Checklist, Forms, and Documents"}
                            </h3>

                            <div className="border rounded-md p-4 bg-muted/5">
                              <div className="flex justify-between items-center mb-4">
                                <div className="flex items-center space-x-2">
                                  <input
                                    type="checkbox"
                                    id="select-all-units"
                                    checked={allUnitsSelected}
                                    onChange={(e) => {
                                      setAllUnitsSelected(e.target.checked);
                                      if (e.target.checked) {
                                        if (assignmentType === "Training") {
                                          // For Training, select all topics
                                          const allTopics: {area: string, topic: string}[] = [];
                                          Object.entries(trainingTopics).forEach(([area, topics]) => {
                                            topics.forEach(topic => {
                                              allTopics.push({ area, topic });
                                            });
                                          });
                                          setSelectedTopics(allTopics);

                                          // Also select all units for backward compatibility
                                          setSelectedUnits(getAllUnits());
                                        } else {
                                          // For CFD, select all units with area and topic info
                                          const allCfdUnits: {area: string, topic: string, unit: string}[] = [];
                                          Object.entries(cfdTopics).forEach(([area, topics]) => {
                                            topics.forEach(topic => {
                                              const topicUnits = cfdUnits[topic] || [];
                                              topicUnits.forEach(unit => {
                                                allCfdUnits.push({ area, topic, unit });
                                              });
                                            });
                                          });
                                          setSelectedCfdUnits(allCfdUnits);

                                          // Also select all units for backward compatibility
                                          setSelectedUnits(getAllUnits());
                                        }
                                      } else {
                                        if (assignmentType === "Training") {
                                          // For Training, clear selected topics
                                          setSelectedTopics([]);
                                        } else {
                                          // For CFD, clear selected units
                                          setSelectedCfdUnits([]);
                                        }
                                        setSelectedUnits([]);
                                      }
                                    }}
                                    className="h-4 w-4 rounded border-gray-300"
                                  />
                                  <Label htmlFor="select-all-units" className="text-sm font-medium">
                                    {assignmentType === "Training"
                                      ? "Select all topics across all areas"
                                      : "Select all documents across all checklists and forms"}
                                  </Label>
                                </div>
                                <div className="flex gap-2">
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      if (assignmentType === "Training") {
                                        // For Training, select all topics
                                        const allTopics: {area: string, topic: string}[] = [];
                                        Object.entries(trainingTopics).forEach(([area, topics]) => {
                                          topics.forEach(topic => {
                                            allTopics.push({ area, topic });
                                          });
                                        });
                                        setSelectedTopics(allTopics);

                                        // Also select all units
                                        setSelectedUnits(getAllUnits());
                                      } else {
                                        // For CFD, select all units with area and topic info
                                        const allCfdUnits: {area: string, topic: string, unit: string}[] = [];
                                        Object.entries(cfdTopics).forEach(([area, topics]) => {
                                          topics.forEach(topic => {
                                            const topicUnits = cfdUnits[topic] || [];
                                            topicUnits.forEach(unit => {
                                              allCfdUnits.push({ area, topic, unit });
                                            });
                                          });
                                        });
                                        setSelectedCfdUnits(allCfdUnits);

                                        // Also select all units for backward compatibility
                                        setSelectedUnits(getAllUnits());
                                      }
                                      setAllUnitsSelected(true);
                                    }}
                                    className="h-7 text-xs"
                                  >
                                    {assignmentType === "Training" ? "Select All Topics" : "Select All"}
                                  </Button>
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      if (assignmentType === "Training") {
                                        // For Training, clear selected topics
                                        setSelectedTopics([]);
                                      } else {
                                        // For CFD, clear selected units
                                        setSelectedCfdUnits([]);
                                      }
                                      // For both, clear selected units
                                      setSelectedUnits([]);
                                      setAllUnitsSelected(false);
                                    }}
                                    disabled={selectedUnits.length === 0}
                                    className="h-7 text-xs"
                                  >
                                    {assignmentType === "Training" ? "Remove All Topics" : "Remove All"}
                                  </Button>
                                </div>
                              </div>

                              {/* Search input for filtering */}
                              <div className="mb-4 relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                                <Input
                                  placeholder={`Search ${assignmentType === "Training" ? "areas, topics, or units" : "checklists, forms, or documents"}...`}
                                  value={programSearchTerm}
                                  onChange={(e) => setProgramSearchTerm(e.target.value)}
                                  className="pl-10 w-full"
                                />
                                {programSearchTerm && (
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 text-muted-foreground hover:text-foreground"
                                    onClick={() => setProgramSearchTerm("")}
                                  >
                                    <X size={16} />
                                  </Button>
                                )}
                              </div>

                              {/* Tree-like selection UI */}
                              <div className="border rounded-md p-3 bg-background max-h-[300px] overflow-y-auto">
                                {/* Get filtered data based on search term */}
                                {(() => {
                                  const { filteredAreas, filteredTopics, filteredUnits, matchCounts } = filterProgramItems(programSearchTerm);

                                  // Display match counts if searching
                                  if (programSearchTerm.trim()) {
                                    const totalMatches = matchCounts.areas + matchCounts.topics + matchCounts.units;
                                    if (totalMatches === 0) {
                                      return (
                                        <div className="text-center py-4 text-muted-foreground">
                                          No matches found for "{programSearchTerm}"
                                        </div>
                                      );
                                    }
                                  }

                                  return filteredAreas.map((area) => {
                                    // Get all topics for this area
                                    const areaTopics = filteredTopics[area] || [];

                                    // Check if this area is in the expanded list
                                    const isExpanded = expandedAreas.includes(area);

                                  return (
                                    <div key={area} className="mb-3">
                                      <Accordion
                                        type="single"
                                        collapsible
                                        value={isExpanded ? area : ""}
                                        onValueChange={(value) => {
                                          if (value === area) {
                                            setExpandedAreas([...expandedAreas, area]);
                                          } else {
                                            setExpandedAreas(expandedAreas.filter(a => a !== area));
                                          }
                                        }}
                                      >
                                        <AccordionItem value={area} className="border-0">
                                          <AccordionTrigger className="py-1 hover:no-underline">
                                            <div className="flex items-center space-x-2">
                                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary/70">
                                                <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                                                <path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4"></path>
                                              </svg>
                                              <span className={`text-sm font-medium ${programSearchTerm && area.toLowerCase().includes(programSearchTerm.toLowerCase()) ? "bg-yellow-100 px-1 rounded" : ""}`}>
                                                {area}
                                              </span>
                                            </div>
                                          </AccordionTrigger>
                                          <AccordionContent className="pt-1 pb-0">
                                            {/* Topics under this area (or Forms under Checklist for CFD) */}
                                            <div className="ml-6 space-y-2">
                                              {areaTopics.map((topic) => {
                                          // Get all units for this topic from filtered units
                                          const topicUnits = filteredUnits[topic] || [];

                                          return (
                                            <div key={topic} className="mb-2">
                                              <div className="flex items-center space-x-2 mb-1">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground">
                                                  <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                                                  <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                                                </svg>
                                                {assignmentType === "Training" ? (
                                                  <div className="flex items-center space-x-2">
                                                    <input
                                                      type="checkbox"
                                                      id={`topic-${topic}`}
                                                      checked={selectedTopics.some(t => t.area === area && t.topic === topic)}
                                                      onChange={(e) => {
                                                        if (e.target.checked) {
                                                          // Add this topic to selected topics
                                                          setSelectedTopics([...selectedTopics, { area, topic }]);

                                                          // Add all units from this topic to selectedUnits
                                                          const unitsToAdd = topicUnits.filter(unit => !selectedUnits.includes(unit));
                                                          setSelectedUnits([...selectedUnits, ...unitsToAdd]);
                                                        } else {
                                                          // Remove this topic from selected topics
                                                          setSelectedTopics(selectedTopics.filter(t => !(t.area === area && t.topic === topic)));

                                                          // Remove all units from this topic
                                                          setSelectedUnits(selectedUnits.filter(unit => !topicUnits.includes(unit)));
                                                        }
                                                      }}
                                                      className="h-4 w-4 rounded border-gray-300"
                                                    />
                                                    <span className={`text-sm font-medium ${programSearchTerm && topic.toLowerCase().includes(programSearchTerm.toLowerCase()) ? "bg-yellow-100 px-1 rounded" : ""}`}>
                                                      {topic}
                                                    </span>
                                                  </div>
                                                ) : (
                                                  <span className={`text-sm ${programSearchTerm && topic.toLowerCase().includes(programSearchTerm.toLowerCase()) ? "bg-yellow-100 px-1 rounded" : ""}`}>
                                                    {topic}
                                                  </span>
                                                )}
                                              </div>

                                              {/* Units under this topic (or Documents under Form for CFD) */}
                                              <div className="ml-6 space-y-1">
                                                {assignmentType === "Training" ? (
                                                  // For Training, just show the units without checkboxes
                                                  topicUnits.map((unit) => (
                                                    <div key={unit} className="flex items-center space-x-2 pl-6">
                                                      <Label className={`text-sm text-muted-foreground ${programSearchTerm && unit.toLowerCase().includes(programSearchTerm.toLowerCase()) ? "bg-yellow-100 px-1 rounded" : ""}`}>
                                                        {unit}
                                                      </Label>
                                                    </div>
                                                  ))
                                                ) : (
                                                  // For CFD, show checkboxes for each unit
                                                  topicUnits.map((unit) => (
                                                    <div key={unit} className="flex items-center space-x-2">
                                                      <input
                                                        type="checkbox"
                                                        id={`unit-${unit}`}
                                                        checked={selectedUnits.includes(unit)}
                                                        onChange={(e) => {
                                                          if (e.target.checked) {
                                                            // Add to selectedUnits for backward compatibility
                                                            setSelectedUnits([...selectedUnits, unit]);

                                                            // Add to selectedCfdUnits with area and topic info
                                                            setSelectedCfdUnits([...selectedCfdUnits, { area, topic, unit }]);
                                                          } else {
                                                            // Remove from selectedUnits
                                                            setSelectedUnits(selectedUnits.filter(u => u !== unit));

                                                            // Remove from selectedCfdUnits
                                                            setSelectedCfdUnits(selectedCfdUnits.filter(
                                                              u => !(u.area === area && u.topic === topic && u.unit === unit)
                                                            ));
                                                          }
                                                        }}
                                                        className="h-4 w-4 rounded border-gray-300"
                                                      />
                                                      <Label htmlFor={`unit-${unit}`} className={`text-sm text-muted-foreground ${programSearchTerm && unit.toLowerCase().includes(programSearchTerm.toLowerCase()) ? "bg-yellow-100 px-1 rounded" : ""}`}>
                                                        {unit}
                                                      </Label>
                                                    </div>
                                                  ))
                                                )}
                                              </div>
                                            </div>
                                          );
                                        })}
                                      </div>
                                          </AccordionContent>
                                        </AccordionItem>
                                      </Accordion>
                                    </div>
                                  );
                                });
                                })()}
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Business Unit Multi-select */}
                        <div className="grid grid-cols-1 gap-2">
                          <div>
                            <MultiSelectCheckbox
                              title="Business Units"
                              options={businessUnits}
                              selectedValues={selectedBusinessUnits}
                              onChange={setSelectedBusinessUnits}
                              maxHeight="200px"
                              className="bg-white"
                            />
                          </div>
                        </div>

                        {/* Organizational Hierarchy */}
                        <div className="grid grid-cols-1 gap-2">
                          {/* Department Groups Multi-select - Only shown when at least one Business Unit is selected */}
                          {selectedBusinessUnits.length > 0 ? (
                            <div>
                              <MultiSelectCheckbox
                                title="Department Groups"
                                options={departmentGroups}
                                selectedValues={selectedDepartmentGroups}
                                onChange={setSelectedDepartmentGroups}
                                maxHeight="200px"
                                className="bg-white"
                              />
                            </div>
                          ) : (
                            <div className="text-sm text-muted-foreground italic p-2 border rounded-md bg-white">
                              Please select at least one Business Unit to display Department Groups
                            </div>
                          )}

                          {/* Dependent Dropdowns - Displayed one by one */}
                          <div className="grid grid-cols-1 gap-4">
                            <div>
                              <MultiSelectCheckbox
                                title="Department"
                                options={availableDepartments}
                                selectedValues={selectedDepartments}
                                onChange={setSelectedDepartments}
                                disabled={availableDepartments.length === 0}
                                maxHeight="200px"
                                className="bg-white"
                              />
                            </div>

                            <div>
                              <MultiSelectCheckbox
                                title="Division"
                                options={availableDivisions}
                                selectedValues={selectedDivisions}
                                onChange={setSelectedDivisions}
                                disabled={availableDivisions.length === 0}
                                maxHeight="200px"
                                className="bg-white"
                              />
                            </div>

                            <div>
                              <MultiSelectCheckbox
                                title="Sub-Division"
                                options={availableSubDivisions}
                                selectedValues={selectedSubDivisions}
                                onChange={setSelectedSubDivisions}
                                disabled={availableSubDivisions.length === 0}
                                maxHeight="200px"
                                className="bg-white"
                              />
                            </div>

                            <div>
                              <MultiSelectCheckbox
                                title="Category"
                                options={availableCategories}
                                selectedValues={selectedCategories}
                                onChange={setSelectedCategories}
                                disabled={availableCategories.length === 0}
                                maxHeight="200px"
                                className="bg-white"
                              />
                            </div>

                            <div>
                              <MultiSelectCheckbox
                                title="Grade"
                                options={availableGrades}
                                selectedValues={selectedGrades}
                                onChange={setSelectedGrades}
                                disabled={availableGrades.length === 0}
                                maxHeight="200px"
                                className="bg-white"
                              />
                            </div>

                            <div>
                              <MultiSelectCheckbox
                                title="Designation"
                                options={availableDesignations}
                                selectedValues={selectedDesignations}
                                onChange={setSelectedDesignations}
                                disabled={availableDesignations.length === 0}
                                maxHeight="200px"
                                className="bg-white"
                              />
                            </div>
                          </div>
                        </div>

                        {/* Employee Selection and Notes */}
                        <div className="grid grid-cols-1 gap-4 mt-2">
                          <div>
                            <h3 className="text-md font-semibold">Employee Selection and Notes</h3>
                            <div className="grid grid-cols-1 gap-4 mt-2">
                              <div>
                                <Accordion type="single" collapsible defaultValue="employee-selection" className="border rounded-md bg-white">
                                  <AccordionItem value="employee-selection" className="border-0">
                                    <AccordionTrigger className="px-3 py-2 hover:no-underline hover:bg-gray-100 focus:bg-gray-100" style={{ backgroundColor: "#f9fafb" }}>
                                      <div className="flex items-center justify-between w-full">
                                        <h4 className="text-sm font-medium">Employee Selection</h4>
                                      </div>
                                    </AccordionTrigger>
                                    <AccordionContent className="px-3 pt-0 pb-3">
                                      <div className="relative">
                                        <Select
                                          onValueChange={(value) => {
                                            // If "all" is selected, select all employees
                                            if (value === "all") {
                                              setSelectedEmployeeIds(users.map(user => user.id));
                                            } else if (value === "clear") {
                                              // If "clear" is selected, clear all selections
                                              setSelectedEmployeeIds([]);
                                            } else {
                                              // Otherwise, toggle the selected employee
                                              if (selectedEmployeeIds.includes(value)) {
                                                setSelectedEmployeeIds(selectedEmployeeIds.filter(id => id !== value));
                                              } else {
                                                setSelectedEmployeeIds([...selectedEmployeeIds, value]);
                                              }
                                            }
                                          }}
                                        >
                                          <SelectTrigger className="w-full">
                                            <SelectValue placeholder={
                                              selectedEmployeeIds.length === 0
                                                ? "Select employees..."
                                                : `${selectedEmployeeIds.length} employee(s) selected`
                                            } />
                                          </SelectTrigger>
                                          <SelectContent>
                                            <SelectItem value="all" className="font-medium text-primary">
                                              Select all employees
                                            </SelectItem>
                                            {selectedEmployeeIds.length > 0 && (
                                              <SelectItem value="clear" className="font-medium text-destructive">
                                                Clear selection
                                              </SelectItem>
                                            )}
                                            <div className="h-px bg-muted my-1"></div>
                                            {users.map((user) => (
                                              <SelectItem
                                                key={user.id}
                                                value={user.id}
                                                className={selectedEmployeeIds.includes(user.id) ? "bg-primary/10" : ""}
                                              >
                                                <div className="flex items-center gap-2">
                                                  <div className={selectedEmployeeIds.includes(user.id) ? "text-primary" : ""}>
                                                    {user.name} ({user.employeeNo}) - {user.department}
                                                  </div>
                                                  {selectedEmployeeIds.includes(user.id) && (
                                                    <div className="ml-auto">
                                                      <Check className="h-4 w-4 text-primary" />
                                                    </div>
                                                  )}
                                                </div>
                                              </SelectItem>
                                            ))}
                                          </SelectContent>
                                        </Select>
                                      </div>
                                    </AccordionContent>
                                  </AccordionItem>
                                </Accordion>

                                {/* Display selected employees */}
                                {selectedEmployeeIds.length > 0 && (
                                  <div className="border rounded-md mt-2 bg-white">
                                    {/* Header without background */}
                                    <div className="px-3 py-2">
                                      <div className="flex items-center justify-between w-full">
                                        <h4 className="text-sm font-medium">
                                          Selected Employees
                                          <span className="text-xs text-muted-foreground ml-2">
                                            {selectedEmployeeIds.length} selected
                                          </span>
                                        </h4>
                                      </div>
                                    </div>
                                    {/* Content */}
                                    <div className="px-3 pt-0 pb-3">
                                      <div className="flex flex-wrap gap-1 mt-2">
                                        {selectedEmployees.map((employee) => (
                                          <SelectedItemTag
                                            key={employee.id}
                                            label={`${employee.name} (${employee.employeeNo})`}
                                            onRemove={() => setSelectedEmployeeIds(selectedEmployeeIds.filter(id => id !== employee.id))}
                                          />
                                        ))}
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                              <div>
                                <Accordion type="single" collapsible defaultValue="notes-input" className="border rounded-md bg-white">
                                  <AccordionItem value="notes-input" className="border-0">
                                    <AccordionTrigger className="px-3 py-2 hover:no-underline">
                                      <div className="flex items-center justify-between w-full">
                                        <h4 className="text-sm font-medium">Notes <span className="text-xs text-muted-foreground">(Optional)</span></h4>
                                      </div>
                                    </AccordionTrigger>
                                    <AccordionContent className="px-3 pt-0 pb-3">
                                      <textarea
                                        id="notes"
                                        placeholder="Add any additional notes"
                                        value={notes}
                                        onChange={(e) => setNotes(e.target.value)}
                                        className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                      />
                                    </AccordionContent>
                                  </AccordionItem>
                                </Accordion>

                                {/* Display notes if entered */}
                                {notes && (
                                  <div className="border rounded-md bg-white mt-2">
                                    {/* Header with neutral background */}
                                    <div className="px-3 py-2" style={{ backgroundColor: "#f9fafb" }}>
                                      <div className="flex items-center justify-between w-full">
                                        <h4 className="text-sm font-medium">Notes Content</h4>
                                      </div>
                                    </div>
                                    {/* Content */}
                                    <div className="px-3 pt-0 pb-3">
                                      <div className="text-sm mt-2">{notes}</div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </ScrollArea>

                    {/* Selected Topics Section - Right Side */}
                    <div className="w-1/3 border-l border-muted/20 overflow-hidden flex flex-col">
                      <div className="p-4 border-b border-muted/20">
                        <h4 className="text-sm font-semibold flex items-center gap-2">
                          <span className="h-2 w-2 rounded-full bg-primary/70"></span>
                          {assignmentType === "Training" ? "Selected Topics" : "Selected Documents"}
                          <span className="text-xs text-muted-foreground ml-2">
                            {assignmentType === "Training"
                              ? `${selectedTopics.length} selected`
                              : `${selectedCfdUnits.length} selected`}
                          </span>
                        </h4>
                      </div>
                      <ScrollArea className="flex-1 overflow-auto">
                        <div className="p-4">
                          {allUnitsSelected ? (
                            <div className="border rounded-md p-4 bg-white">
                              <div className="text-center font-medium">
                                {assignmentType === "Training"
                                  ? "All topics across all areas selected"
                                  : "All documents across all checklists and forms selected"}
                              </div>
                            </div>
                          ) : (assignmentType === "Training" ? selectedTopics.length > 0 : selectedCfdUnits.length > 0) ? (
                            <div className="border rounded-md overflow-hidden">
                              <div className="p-4 bg-white">
                                {assignmentType === "Training" ? (
                                  <SelectedUnitsWithHierarchy
                                    selectedUnits={selectedUnits}
                                    areas={trainingAreas}
                                    topics={trainingTopics}
                                    units={trainingUnits}
                                    assignmentType="Training"
                                    selectedTopics={selectedTopics}
                                  />
                                ) : (
                                  <SelectedUnitsWithHierarchy
                                    selectedUnits={selectedUnits}
                                    areas={cfdAreas}
                                    topics={cfdTopics}
                                    units={cfdUnits}
                                    assignmentType="CFD"
                                    selectedCfdUnits={selectedCfdUnits}
                                  />
                                )}
                              </div>
                            </div>
                          ) : (
                            <div className="text-sm text-muted-foreground italic p-2">
                              {assignmentType === "Training" ? "No topics selected" : "No documents selected"}
                            </div>
                          )}
                        </div>
                      </ScrollArea>
                    </div>
                </div>
                <DialogFooter className="px-6 py-4 border-t">
                  <Button variant="outline" onClick={() => setDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAssignTraining}>Assign </Button>
                </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>



          {/* Active Filters Display */}
          {activeFilterCount > 0 && (
            <div className="mt-4">
              <div className="border rounded-md bg-white">
                {/* Header without background */}
                <div className="px-3 py-2">
                  <div className="flex items-center justify-between w-full">
                    <h4 className="text-sm font-medium">
                      Active Filters
                      <span className="text-xs text-muted-foreground ml-2">
                        {activeFilterCount} active
                      </span>
                    </h4>
                  </div>
                </div>
                {/* Content */}
                <div className="px-3 pt-0 pb-3">
                  <div className="flex flex-wrap gap-1 mt-2">
                    {Object.entries(filters).map(([key, values]) => {
                      if (Array.isArray(values) && values.length > 0) {
                        return values.map((value) => (
                          <SelectedItemTag
                            key={`${key}-${value}`}
                            label={`${key}: ${value}`}
                            onRemove={() => handleFilterChange(key, value)}
                          />
                        ));
                      }
                      return null;
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Summary Panel */}
          {(activeFilterCount > 0 || searchTerm) && (
            <div className="mb-6">
              <Card className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-semibold text-blue-900">Applied Filters Summary</h3>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                    {activeFilterCount} filter{activeFilterCount !== 1 ? 's' : ''} active
                  </span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                  {searchTerm && (
                    <div>
                      <span className="text-blue-700 font-medium">Search:</span>
                      <span className="ml-2 bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">{searchTerm}</span>
                    </div>
                  )}
                  {filters.businessUnit.length > 0 && (
                    <div>
                      <span className="text-blue-700 font-medium">Business Units:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {filters.businessUnit.map(unit => (
                          <span key={unit} className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">{unit}</span>
                        ))}
                      </div>
                    </div>
                  )}
                  {filters.departmentGroup.length > 0 && (
                    <div>
                      <span className="text-blue-700 font-medium">Services:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {filters.departmentGroup.map(dept => (
                          <span key={dept} className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">{dept}</span>
                        ))}
                      </div>
                    </div>
                  )}
                  {filters.designation.length > 0 && (
                    <div>
                      <span className="text-blue-700 font-medium">Roles:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {filters.designation.map(role => (
                          <span key={role} className="bg-pink-100 text-pink-800 px-2 py-1 rounded text-xs">{role}</span>
                        ))}
                      </div>
                    </div>
                  )}
                  {filters.status.length > 0 && (
                    <div>
                      <span className="text-blue-700 font-medium">Status:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {filters.status.map(status => (
                          <span key={status} className={`px-2 py-1 rounded text-xs ${
                            status === "Completed" ? "bg-green-100 text-green-800" :
                            status === "In Progress" ? "bg-blue-100 text-blue-800" :
                            "bg-yellow-100 text-yellow-800"
                          }`}>{status}</span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            </div>
          )}

          <div className="rounded-lg border overflow-hidden shadow-lg bg-white">
            <Table className="w-full">
              <TableHeader className="bg-gradient-to-r from-slate-50 to-slate-100 border-b-2 border-slate-200">
                <TableRow className="hover:bg-slate-100/50 transition-colors duration-200">
                  <TableHead className="font-semibold text-slate-700">
                    <div className="flex items-center space-x-2">
                      <div className="flex flex-col">
                        <span>Training Program</span>
                        <span className="text-xs text-slate-500 font-normal">(Area → Topic → Unit)</span>
                      </div>
                    </div>
                  </TableHead>
                  <TableHead className="hidden md:table-cell font-semibold text-slate-700">
                    <div className="flex items-center gap-1">
                      <span>Business Unit</span>
                      <DropdownMenu open={openFilterDropdown === "businessUnit"} onOpenChange={(open) => setOpenFilterDropdown(open ? "businessUnit" : null)}>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8 p-0 rounded-full hover:bg-muted/50 transition-colors duration-200">
                            <Filter size={14} className={`${filters.businessUnit ? "text-primary" : "text-muted-foreground"}`} />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[240px] p-1 shadow-md border-muted/40">
                          <div className="p-3">
                            <h4 className="mb-3 text-sm font-medium text-primary/80">Filter by Business Unit</h4>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-xs h-7 hover:bg-muted/50"
                                  onClick={() => handleFilterChange("businessUnit", "all")}
                                >
                                  Clear
                                </Button>
                                <span className="text-xs text-muted-foreground">
                                  {filters.businessUnit.length} selected
                                </span>
                              </div>
                              <div className="max-h-[200px] overflow-y-auto pr-1 space-y-1">
                                {businessUnits.map((unit) => (
                                  <div key={unit} className="flex items-center space-x-2">
                                    <Checkbox
                                      id={`business-unit-${unit}`}
                                      checked={isFilterSelected("businessUnit", unit)}
                                      onCheckedChange={() => handleFilterChange("businessUnit", unit)}
                                    />
                                    <label
                                      htmlFor={`business-unit-${unit}`}
                                      className="text-sm cursor-pointer"
                                    >
                                      {unit}
                                    </label>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableHead>
                  <TableHead className="hidden lg:table-cell font-semibold text-slate-700">
                    <div className="flex items-center gap-1">
                      <span>Department Group</span>
                      <DropdownMenu open={openFilterDropdown === "departmentGroup"} onOpenChange={(open) => setOpenFilterDropdown(open ? "departmentGroup" : null)}>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
                            <Filter size={12} className={`${filters.departmentGroup ? "text-primary" : "text-muted-foreground"}`} />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[200px]">
                          <div className="p-2">
                            <h4 className="mb-2 text-sm font-medium">Filter by Department Group</h4>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-xs h-7 hover:bg-muted/50"
                                  onClick={() => handleFilterChange("departmentGroup", "all")}
                                >
                                  Clear
                                </Button>
                                <span className="text-xs text-muted-foreground">
                                  {filters.departmentGroup.length} selected
                                </span>
                              </div>
                              <div className="max-h-[200px] overflow-y-auto pr-1 space-y-1">
                                {departmentGroups.map((group) => (
                                  <div key={group} className="flex items-center space-x-2">
                                    <Checkbox
                                      id={`dept-group-${group}`}
                                      checked={isFilterSelected("departmentGroup", group)}
                                      onCheckedChange={() => handleFilterChange("departmentGroup", group)}
                                    />
                                    <label
                                      htmlFor={`dept-group-${group}`}
                                      className="text-sm cursor-pointer"
                                    >
                                      {group}
                                    </label>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableHead>
                  <TableHead className="hidden lg:table-cell font-semibold text-slate-700">
                    <div className="flex items-center gap-1">
                      <span>Department</span>
                      <DropdownMenu open={openFilterDropdown === "department"} onOpenChange={(open) => setOpenFilterDropdown(open ? "department" : null)}>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
                            <Filter size={12} className={`${filters.department ? "text-primary" : "text-muted-foreground"}`} />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[200px]">
                          <div className="p-2">
                            <h4 className="mb-2 text-sm font-medium">Filter by Department</h4>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-xs h-7 hover:bg-muted/50"
                                  onClick={() => handleFilterChange("department", "all")}
                                >
                                  Clear
                                </Button>
                                <span className="text-xs text-muted-foreground">
                                  {filters.department.length} selected
                                </span>
                              </div>
                              <div className="max-h-[200px] overflow-y-auto pr-1 space-y-1">
                                {Object.values(departmentsByGroup).flat().map((dept) => (
                                  <div key={dept} className="flex items-center space-x-2">
                                    <Checkbox
                                      id={`dept-${dept}`}
                                      checked={isFilterSelected("department", dept)}
                                      onCheckedChange={() => handleFilterChange("department", dept)}
                                    />
                                    <label
                                      htmlFor={`dept-${dept}`}
                                      className="text-sm cursor-pointer"
                                    >
                                      {dept}
                                    </label>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableHead>
                  <TableHead className="hidden xl:table-cell font-semibold text-slate-700">
                    <div className="flex items-center gap-1">
                      <span>Division</span>
                      <DropdownMenu open={openFilterDropdown === "division"} onOpenChange={(open) => setOpenFilterDropdown(open ? "division" : null)}>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
                            <Filter size={12} className={`${filters.division ? "text-primary" : "text-muted-foreground"}`} />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[200px]">
                          <div className="p-2">
                            <h4 className="mb-2 text-sm font-medium">Filter by Division</h4>
                            <Select value={filters.division.length > 0 ? filters.division[0] : ""} onValueChange={(value) => handleFilterChange("division", value)}>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="All" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">All</SelectItem>
                                {Object.values(divisionsByDepartment).flat().map((div) => (
                                  <SelectItem key={div} value={div}>{div}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableHead>
                  <TableHead className="hidden xl:table-cell font-semibold text-slate-700">
                    <div className="flex items-center gap-1">
                      <span>Sub-Division</span>
                      <DropdownMenu open={openFilterDropdown === "subDivision"} onOpenChange={(open) => setOpenFilterDropdown(open ? "subDivision" : null)}>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
                            <Filter size={12} className={`${filters.subDivision ? "text-primary" : "text-muted-foreground"}`} />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[200px]">
                          <div className="p-2">
                            <h4 className="mb-2 text-sm font-medium">Filter by Sub-Division</h4>
                            <Select value={filters.subDivision.length > 0 ? filters.subDivision[0] : ""} onValueChange={(value) => handleFilterChange("subDivision", value)}>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="All" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">All</SelectItem>
                                {Object.values(subDivisionsByDivision).flat().map((subDiv) => (
                                  <SelectItem key={subDiv} value={subDiv}>{subDiv}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableHead>
                  <TableHead className="hidden xl:table-cell font-semibold text-slate-700">
                    <div className="flex items-center gap-1">
                      <span>Category</span>
                      <DropdownMenu open={openFilterDropdown === "category"} onOpenChange={(open) => setOpenFilterDropdown(open ? "category" : null)}>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
                            <Filter size={12} className={`${filters.category ? "text-primary" : "text-muted-foreground"}`} />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[200px]">
                          <div className="p-2">
                            <h4 className="mb-2 text-sm font-medium">Filter by Category</h4>
                            <Select value={filters.category.length > 0 ? filters.category[0] : ""} onValueChange={(value) => handleFilterChange("category", value)}>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="All" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">All</SelectItem>
                                {Object.values(categoriesBySubDivision).flat().map((cat) => (
                                  <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableHead>
                  <TableHead className="hidden lg:table-cell font-semibold text-slate-700">
                    <div className="flex items-center gap-1">
                      <span>Grade</span>
                      <DropdownMenu open={openFilterDropdown === "grade"} onOpenChange={(open) => setOpenFilterDropdown(open ? "grade" : null)}>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
                            <Filter size={12} className={`${filters.grade ? "text-primary" : "text-muted-foreground"}`} />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[200px]">
                          <div className="p-2">
                            <h4 className="mb-2 text-sm font-medium">Filter by Grade</h4>
                            <Select value={filters.grade.length > 0 ? filters.grade[0] : ""} onValueChange={(value) => handleFilterChange("grade", value)}>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="All" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">All</SelectItem>
                                {Object.values(gradesByCategory).flat().map((grade) => (
                                  <SelectItem key={grade} value={grade}>{grade}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableHead>
                  <TableHead className="hidden lg:table-cell font-semibold text-slate-700">
                    <div className="flex items-center gap-1">
                      <span>Designation</span>
                      <DropdownMenu open={openFilterDropdown === "designation"} onOpenChange={(open) => setOpenFilterDropdown(open ? "designation" : null)}>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
                            <Filter size={12} className={`${filters.designation ? "text-primary" : "text-muted-foreground"}`} />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[200px]">
                          <div className="p-2">
                            <h4 className="mb-2 text-sm font-medium">Filter by Designation</h4>
                            <Select value={filters.designation.length > 0 ? filters.designation[0] : ""} onValueChange={(value) => handleFilterChange("designation", value)}>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="All" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">All</SelectItem>
                                {Object.values(designationsByGrade).flat().map((designation) => (
                                  <SelectItem key={designation} value={designation}>{designation}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold text-slate-700">
                    <div className="flex items-center gap-1">
                      <span>Status</span>
                      <DropdownMenu open={openFilterDropdown === "status"} onOpenChange={(open) => setOpenFilterDropdown(open ? "status" : null)}>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
                            <Filter size={12} className={`${filters.status ? "text-primary" : "text-muted-foreground"}`} />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-[200px]">
                          <div className="p-2">
                            <h4 className="mb-2 text-sm font-medium">Filter by Status</h4>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-xs h-7 hover:bg-muted/50"
                                  onClick={() => handleFilterChange("status", "all")}
                                >
                                  Clear
                                </Button>
                                <span className="text-xs text-muted-foreground">
                                  {filters.status.length} selected
                                </span>
                              </div>
                              <div className="max-h-[200px] overflow-y-auto pr-1 space-y-1">
                                {statuses.map((status) => (
                                  <div key={status} className="flex items-center space-x-2">
                                    <Checkbox
                                      id={`status-${status}`}
                                      checked={isFilterSelected("status", status)}
                                      onCheckedChange={() => handleFilterChange("status", status)}
                                    />
                                    <label
                                      htmlFor={`status-${status}`}
                                      className="text-sm cursor-pointer"
                                    >
                                      {status}
                                    </label>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableHead>
                  <TableHead className="text-right font-semibold text-slate-700">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentItems.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={12} className="text-center py-8 text-muted-foreground">
                      {trainingAssignments.length === 0 ? (
                        <div className="flex flex-col items-center gap-2">
                          <p>No training assignments found</p>
                          <p className="text-sm">Click "Assign Training" or "Assign CFD" to add a new assignment</p>
                        </div>
                      ) : (
                        "No matching training assignments found with the current filters"
                      )}
                    </TableCell>
                  </TableRow>
                ) : (
                  currentItems.map((assignment: TrainingAssignment) => {
                    const isExpanded = expandedRows.has(assignment.id);

                    return (
                      <React.Fragment key={assignment.id}>
                        {/* Main Row */}
                        <TableRow className="hover:bg-slate-50/80 transition-colors duration-200 border-b border-slate-100">
                          <TableCell className="py-4">
                            <div className="flex items-center gap-3">
                              <button
                                onClick={() => toggleRowExpansion(assignment.id)}
                                className="flex items-center justify-center w-6 h-6 rounded-full hover:bg-slate-200 transition-colors duration-200"
                              >
                                <ChevronDown
                                  size={16}
                                  className={`text-slate-600 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
                                />
                              </button>
                              <button
                                className="font-semibold text-blue-600 hover:text-blue-700 hover:underline transition-colors duration-200 text-left"
                                onClick={() => {
                                  setSelectedProgram(assignment.trainingProgram);
                                  setProgramDetailsOpen(true);
                                }}
                              >
                                {assignment.trainingProgram === "All Units Selected" ? (
                                  "All Units Selected"
                                ) : (
                                  assignment.trainingProgram.split(' | ').length > 1
                                    ? `${assignment.trainingProgram.split(' | ').length} Units Selected`
                                    : assignment.trainingProgram.split(' → ')[0]
                                )}
                              </button>
                            </div>
                          </TableCell>
                          <TableCell className="hidden md:table-cell py-4">
                            <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              {assignment.businessUnit}
                            </span>
                          </TableCell>
                          <TableCell className="hidden lg:table-cell py-4 text-slate-700">{assignment.departmentGroup}</TableCell>
                          <TableCell className="hidden lg:table-cell py-4 text-slate-700">{assignment.department}</TableCell>
                          <TableCell className="hidden xl:table-cell py-4 text-slate-600">{assignment.division}</TableCell>
                          <TableCell className="hidden xl:table-cell py-4 text-slate-600">{assignment.subDivision}</TableCell>
                          <TableCell className="hidden xl:table-cell py-4 text-slate-600">{assignment.category}</TableCell>
                          <TableCell className="hidden lg:table-cell py-4">
                            <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                              {assignment.grade}
                            </span>
                          </TableCell>
                          <TableCell className="hidden lg:table-cell py-4">
                            <div className="flex flex-wrap gap-1">
                              {assignment.designation.split(', ').map((designation, index) => {
                                // Color coding for different designations
                                const getDesignationColor = (designation: string) => {
                                  if (designation.toLowerCase().includes('nurse')) {
                                    return 'bg-pink-100 text-pink-800';
                                  } else if (designation.toLowerCase().includes('technician')) {
                                    return 'bg-purple-100 text-purple-800';
                                  } else if (designation.toLowerCase().includes('physician') || designation.toLowerCase().includes('doctor')) {
                                    return 'bg-blue-100 text-blue-800';
                                  } else if (designation.toLowerCase().includes('assistant')) {
                                    return 'bg-orange-100 text-orange-800';
                                  } else if (designation.toLowerCase().includes('manager') || designation.toLowerCase().includes('director')) {
                                    return 'bg-emerald-100 text-emerald-800';
                                  } else {
                                    return 'bg-gray-100 text-gray-800';
                                  }
                                };

                                return (
                                  <span key={index} className={`text-xs font-medium px-2 py-1 rounded-full ${getDesignationColor(designation)}`}>
                                    {designation}
                                  </span>
                                );
                              })}
                            </div>
                            {assignment.employeeIds ? (
                              assignment.employeeIds.length > 1 && (
                                <div className="mt-1">
                                  <span className="text-xs font-medium bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                    {assignment.employeeIds.length} Employees
                                  </span>
                                </div>
                              )
                            ) : assignment.employeeName && (
                              <div className="mt-1">
                                <span className="text-xs font-medium bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                  1 Employee
                                </span>
                              </div>
                            )}
                          </TableCell>
                          <TableCell className="py-4">
                            <div className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold shadow-sm ${
                              assignment.status === "Completed"
                                ? "bg-green-100 text-green-800 border border-green-200"
                                : assignment.status === "In Progress"
                                ? "bg-blue-100 text-blue-800 border border-blue-200"
                                : "bg-yellow-100 text-yellow-800 border border-yellow-200"
                            }`}>
                              {assignment.status === "Completed" && (
                                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              )}
                              {assignment.status === "In Progress" && (
                                <svg className="w-3 h-3 mr-1 animate-spin" fill="currentColor" viewBox="0 0 20 20">
                                  <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm6 14a6 6 0 110-12 6 6 0 010 12z" />
                                </svg>
                              )}
                              {assignment.status === "Not Started" && (
                                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                </svg>
                              )}
                              {assignment.status}
                            </div>
                          </TableCell>
                          <TableCell className="text-right py-4">
                            <div className="flex items-center justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-9 w-9 p-0 rounded-full hover:bg-blue-50 hover:border-blue-200 border border-transparent transition-all duration-200"
                                onClick={() => {
                                  setSelectedAssignment(assignment);
                                  setDetailsDialogOpen(true);
                                }}
                              >
                                <Eye size={16} className="text-blue-600" />
                                <span className="sr-only">View</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-9 w-9 p-0 rounded-full hover:bg-emerald-50 hover:border-emerald-200 border border-transparent transition-all duration-200"
                                onClick={() => {
                                  setAssignmentToEdit(assignment);
                                  setEditDialogOpen(true);
                                }}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-emerald-600">
                                  <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                                  <path d="m15 5 4 4"></path>
                                </svg>
                                <span className="sr-only">Edit</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-9 w-9 p-0 rounded-full hover:bg-red-50 hover:border-red-200 border border-transparent transition-all duration-200"
                                onClick={() => {
                                  // Delete the assignment
                                  setTrainingAssignments(trainingAssignments.filter(a => a.id !== assignment.id));

                                  const employeeText = assignment.employeeIds
                                    ? (assignment.employeeIds.length > 1 ? `${assignment.employeeIds.length} employees` : "1 employee")
                                    : (assignment.employeeName || "the employee");

                                  toast({
                                    title: "Assignment Deleted",
                                    description: `Training assignment for ${employeeText} has been deleted.`,
                                    variant: "destructive",
                                  });
                                }}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-red-600">
                                  <path d="M3 6h18"></path>
                                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                </svg>
                                <span className="sr-only">Delete</span>
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>

                        {/* Expanded Row Content */}
                        {isExpanded && (
                          <TableRow className="bg-slate-50/50">
                            <TableCell colSpan={12} className="py-6 px-8">
                              <div className="bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden">
                                <div className="px-4 py-3 bg-slate-100 border-b border-slate-200">
                                  <h4 className="font-semibold text-slate-800 flex items-center gap-2">
                                    <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    Assignment Details
                                  </h4>
                                </div>

                                {/* Create rows for each employee assignment combination */}
                                <div className="overflow-x-auto">
                                  <table className="w-full">
                                    <thead className="bg-slate-50">
                                      <tr className="text-xs text-slate-600 uppercase tracking-wider">
                                        <th className="px-4 py-3 text-left font-medium">Business Unit</th>
                                        <th className="px-4 py-3 text-left font-medium">Department Group</th>
                                        <th className="px-4 py-3 text-left font-medium">Department</th>
                                        <th className="px-4 py-3 text-left font-medium">Division</th>
                                        <th className="px-4 py-3 text-left font-medium">Sub-Division</th>
                                        <th className="px-4 py-3 text-left font-medium">Category</th>
                                        <th className="px-4 py-3 text-left font-medium">Grade</th>
                                        <th className="px-4 py-3 text-left font-medium">Designation</th>
                                      </tr>
                                    </thead>
                                    <tbody className="divide-y divide-slate-200">
                                      {(() => {
                                        // Split the comma-separated values to create individual rows
                                        const businessUnits = assignment.businessUnit.split(', ');
                                        const departmentGroups = assignment.departmentGroup.split(', ');
                                        const departments = assignment.department.split(', ');
                                        const divisions = assignment.division.split(', ');
                                        const subDivisions = assignment.subDivision.split(', ');
                                        const categories = assignment.category.split(', ');
                                        const grades = assignment.grade.split(', ');
                                        const designations = assignment.designation.split(', ');

                                        // Find the maximum length to create rows
                                        const maxLength = Math.max(
                                          businessUnits.length,
                                          departmentGroups.length,
                                          departments.length,
                                          divisions.length,
                                          subDivisions.length,
                                          categories.length,
                                          grades.length,
                                          designations.length
                                        );

                                        // Create rows
                                        const rows = [];
                                        for (let i = 0; i < maxLength; i++) {
                                          rows.push(
                                            <tr key={i} className="hover:bg-slate-50 transition-colors">
                                              <td className="px-4 py-3 text-sm">
                                                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                  {businessUnits[i % businessUnits.length]}
                                                </span>
                                              </td>
                                              <td className="px-4 py-3 text-sm text-slate-700">
                                                {departmentGroups[i % departmentGroups.length]}
                                              </td>
                                              <td className="px-4 py-3 text-sm text-slate-700">
                                                {departments[i % departments.length]}
                                              </td>
                                              <td className="px-4 py-3 text-sm text-slate-600">
                                                {divisions[i % divisions.length]}
                                              </td>
                                              <td className="px-4 py-3 text-sm text-slate-600">
                                                {subDivisions[i % subDivisions.length]}
                                              </td>
                                              <td className="px-4 py-3 text-sm text-slate-600">
                                                {categories[i % categories.length]}
                                              </td>
                                              <td className="px-4 py-3 text-sm">
                                                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                                  {grades[i % grades.length]}
                                                </span>
                                              </td>
                                              <td className="px-4 py-3 text-sm">
                                                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                  {designations[i % designations.length]}
                                                </span>
                                              </td>
                                            </tr>
                                          );
                                        }
                                        return rows;
                                      })()}
                                    </tbody>
                                  </table>
                                </div>

                                {/* Additional Information */}
                                <div className="px-4 py-3 bg-slate-50 border-t border-slate-200">
                                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                    <div>
                                      <span className="text-slate-600 font-medium">Training Program:</span>
                                      <p className="text-slate-800 mt-1">{assignment.trainingProgram}</p>
                                    </div>
                                    <div>
                                      <span className="text-slate-600 font-medium">Assigned Date:</span>
                                      <p className="text-slate-800 mt-1">{assignment.assignedDate}</p>
                                    </div>
                                    <div>
                                      <span className="text-slate-600 font-medium">Employee Count:</span>
                                      <p className="text-slate-800 mt-1">
                                        {assignment.employeeIds ? assignment.employeeIds.length : 1} Employee{assignment.employeeIds && assignment.employeeIds.length > 1 ? 's' : ''}
                                      </p>
                                    </div>
                                  </div>

                                  {assignment.notes && (
                                    <div className="mt-4 pt-4 border-t border-slate-200">
                                      <span className="text-slate-600 font-medium">Notes:</span>
                                      <p className="text-slate-700 mt-1">{assignment.notes}</p>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </React.Fragment>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>

          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-8 gap-4">
            <div className="text-sm text-slate-600 bg-slate-50 border border-slate-200 px-4 py-2 rounded-lg shadow-sm">
              <span className="font-medium">Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, filteredAssignments.length)}</span>
              <span className="text-slate-500"> of </span>
              <span className="font-medium">{filteredAssignments.length} entries</span>
            </div>

            <Pagination className="self-center sm:self-auto">
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => goToPage(currentPage - 1)}
                    className={`${currentPage === 1 ? "pointer-events-none opacity-50" : ""} transition-all duration-200 hover:bg-muted/50`}
                  />
                </PaginationItem>

                {Array.from({ length: Math.min(totalPages, 5) }).map((_, index) => {
                  let pageNumber = currentPage - 2 + index;
                  if (pageNumber <= 0) pageNumber = index + 1;
                  if (pageNumber > totalPages) return null;

                  return (
                    <PaginationItem key={index}>
                      <PaginationLink
                        onClick={() => goToPage(pageNumber)}
                        isActive={currentPage === pageNumber}
                        className="transition-all duration-200 hover:bg-muted/50"
                      >
                        {pageNumber}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => goToPage(currentPage + 1)}
                    className={`${currentPage === totalPages ? "pointer-events-none opacity-50" : ""} transition-all duration-200 hover:bg-muted/50`}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </Card>
      </motion.div>
    </motion.div>

    {/* Training Program Details Dialog */}
    <Dialog open={programDetailsOpen} onOpenChange={setProgramDetailsOpen}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] border-muted/40 shadow-lg flex flex-col">
        <DialogHeader className="bg-muted/10 pb-2">
          <DialogTitle className="text-xl font-semibold">Training Program Details</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            View the details of the assigned training program.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4 overflow-y-auto pr-2">
          <div className="grid grid-cols-1 gap-4">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <h3 className="text-md font-semibold text-primary/90">Assigned Training Details</h3>
                <div className="px-2 py-1 bg-primary/10 rounded-md text-xs font-medium text-primary">
                  {selectedProgram === "All Units Selected" || selectedProgram === "All Documents Selected"
                    ? (selectedProgram === "All Units Selected" ? "Training" : "CFD")
                    : (selectedProgram.includes("Checklist") ? "CFD" : "Training")}
                </div>
              </div>

              {selectedProgram === "All Units Selected" || selectedProgram === "All Documents Selected" ? (
                <div className="bg-muted/30 p-5 rounded-md border border-muted/40 shadow-sm">
                  <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-primary/70"></span>
                    Selected Training Paths
                  </h4>
                  <div className="max-h-[400px] overflow-y-auto pr-2">
                    {(() => {
                      // For "All Units Selected", create a comprehensive list of all area-topic combinations
                      const isTraining = selectedProgram === "All Units Selected";

                      if (isTraining) {
                        // For Training, create selectedTopics with all area-topic combinations
                        const allSelectedTopics: {area: string, topic: string}[] = [];

                        trainingAreas.forEach((area: string) => {
                          const areaTopics = trainingTopics[area] || [];
                          areaTopics.forEach((topic: string) => {
                            allSelectedTopics.push({ area, topic });
                          });
                        });

                        return (
                          <SelectedUnitsWithHierarchy
                            selectedUnits={[]}
                            areas={trainingAreas}
                            topics={trainingTopics}
                            units={trainingUnits}
                            assignmentType="Training"
                            selectedTopics={allSelectedTopics}
                          />
                        );
                      } else {
                        // For CFD, create selectedCfdUnits with all area-topic-unit combinations
                        const allSelectedCfdUnits: {area: string, topic: string, unit: string}[] = [];

                        cfdAreas.forEach((area: string) => {
                          const areaTopics = cfdTopics[area] || [];
                          areaTopics.forEach((topic: string) => {
                            const topicUnits = cfdUnits[topic] || [];
                            topicUnits.forEach((unit: string) => {
                              allSelectedCfdUnits.push({ area, topic, unit });
                            });
                          });
                        });

                        return (
                          <SelectedUnitsWithHierarchy
                            selectedUnits={[]}
                            areas={cfdAreas}
                            topics={cfdTopics}
                            units={cfdUnits}
                            assignmentType="CFD"
                            selectedCfdUnits={allSelectedCfdUnits}
                          />
                        );
                      }
                    })()}
                  </div>
                </div>
              ) : (
                <div className="bg-muted/30 p-5 rounded-md border border-muted/40 shadow-sm">
                  <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-primary/70"></span>
                    Selected Training Paths
                  </h4>
                  <div className="max-h-[400px] overflow-y-auto pr-2">
                    {(() => {
                      // Extract units from the program string
                      const programParts = selectedProgram.split(' | ').map(program => {
                        // Both Training and CFD formats now use the same format: "Area -> Topic -> Unit"
                        return program.split(' -> ');
                      });

                      // Create selectedTopics or selectedCfdUnits based on assignment type
                      if (selectedProgram.includes("Checklist") || selectedProgram === "All Documents Selected") {
                        // For CFD
                        const selectedCfdUnits = programParts.map(parts => ({
                          area: parts[0],
                          topic: parts[1],
                          unit: parts[2]
                        }));

                        return (
                          <SelectedUnitsWithHierarchy
                            selectedUnits={[]}
                            areas={cfdAreas}
                            topics={cfdTopics}
                            units={cfdUnits}
                            assignmentType="CFD"
                            selectedCfdUnits={selectedCfdUnits}
                          />
                        );
                      } else {
                        // For Training
                        const selectedTopics = programParts.map(parts => ({
                          area: parts[0],
                          topic: parts[1]
                        }));

                        return (
                          <SelectedUnitsWithHierarchy
                            selectedUnits={[]}
                            areas={trainingAreas}
                            topics={trainingTopics}
                            units={trainingUnits}
                            assignmentType="Training"
                            selectedTopics={selectedTopics}
                          />
                        );
                      }
                    })()}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <DialogFooter className="border-t border-muted/20 pt-4 mt-auto">
          <DialogClose asChild>
            <Button type="button" variant="secondary" className="transition-all duration-200 hover:bg-muted/80 hover:border-primary/30 shadow-sm">
              Close
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* Assignment Details Dialog */}
    <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
      <DialogContent className="sm:max-w-[700px] border-muted/40 shadow-lg max-h-[90vh] flex flex-col">
        <DialogHeader className="bg-muted/10 pb-2">
          <DialogTitle className="text-xl font-semibold">Training Assignment Details</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            View detailed information about this training assignment.
          </DialogDescription>
        </DialogHeader>

        {selectedAssignment && (
          <div className="py-4 overflow-y-auto pr-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Employee Information */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-md font-semibold mb-3 text-primary/90 flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary/70">
                      <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                      <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                    Employee Information
                  </h3>
                  <div className="bg-muted/30 p-4 rounded-md border border-muted/40 shadow-sm space-y-3">
                    {/* <div>
                      <span className="text-xs text-muted-foreground block">Name</span>
                      <span className="text-sm font-medium">{selectedAssignment.employeeName}</span>
                    </div> */}
                    <div>
                      <span className="text-xs text-muted-foreground block">Business Unit</span>
                      <span className="text-sm font-medium">{selectedAssignment.businessUnit}</span>
                    </div>
                    <div>
                      <span className="text-xs text-muted-foreground block">Department Group</span>
                      <span className="text-sm font-medium">{selectedAssignment.departmentGroup}</span>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <span className="text-xs text-muted-foreground block">Department</span>
                        <span className="text-sm font-medium">{selectedAssignment.department}</span>
                      </div>
                      <div>
                        <span className="text-xs text-muted-foreground block">Division</span>
                        <span className="text-sm font-medium">{selectedAssignment.division}</span>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <span className="text-xs text-muted-foreground block">Sub-Division</span>
                        <span className="text-sm font-medium">{selectedAssignment.subDivision}</span>
                      </div>
                      <div>
                        <span className="text-xs text-muted-foreground block">Category</span>
                        <span className="text-sm font-medium">{selectedAssignment.category}</span>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <span className="text-xs text-muted-foreground block">Grade</span>
                        <span className="text-sm font-medium">{selectedAssignment.grade}</span>
                      </div>
                      <div>
                        <span className="text-xs text-muted-foreground block">Designation</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {selectedAssignment.designation.split(', ').map((designation, index) => (
                            <span key={index} className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full">
                              {designation}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Assignment Status */}
                <div>
                  <h3 className="text-md font-semibold mb-3 text-primary/90 flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary/70">
                      <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                      <path d="m9 12 2 2 4-4"></path>
                    </svg>
                    Assignment Status
                  </h3>
                  <div className="bg-muted/30 p-4 rounded-md border border-muted/40 shadow-sm space-y-3">
                    <div>
                      <span className="text-xs text-muted-foreground block">Status</span>
                      <div className={`inline-flex items-center px-2.5 py-0.5 mt-1 rounded-full text-xs font-medium ${
                        selectedAssignment.status === "Completed"
                          ? "bg-green-100 text-green-800"
                          : selectedAssignment.status === "In Progress"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-yellow-100 text-yellow-800"
                      }`}>
                        {selectedAssignment.status}
                      </div>
                    </div>
                    <div>
                      <span className="text-xs text-muted-foreground block">Assigned Date</span>
                      <span className="text-sm font-medium">{selectedAssignment.assignedDate}</span>
                    </div>
                    {selectedAssignment.notes && (
                      <div>
                        <span className="text-xs text-muted-foreground block">Notes</span>
                        <span className="text-sm">{selectedAssignment.notes}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Training Program */}
              <div>
                <h3 className="text-md font-semibold mb-3 text-primary/90 flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary/70">
                    <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"></path>
                  </svg>
                  {assignmentType === "CFD" ? "Checklist, Forms, and Documents" : "Training Program"}
                </h3>
                <div className="bg-muted/30 p-4 rounded-md border border-muted/40 shadow-sm">
                  {selectedAssignment.trainingProgram === "All Units Selected" || selectedAssignment.trainingProgram === "All Documents Selected" ? (
                    <div>
                      <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                        <span className="h-2 w-2 rounded-full bg-primary/70"></span>
                        {selectedAssignment.trainingProgram === "All Units Selected" ? "All Units Selected" : "All Documents Selected"}
                      </h4>
                      <div className="text-sm text-muted-foreground mb-2">
                        {selectedAssignment.trainingProgram === "All Units Selected"
                          ? "All training units across all areas and topics have been assigned."
                          : "All documents across all checklists and forms have been assigned."}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2 text-xs gap-1"
                        onClick={() => {
                          setSelectedProgram(selectedAssignment.trainingProgram);
                          setProgramDetailsOpen(true);
                          setDetailsDialogOpen(false);
                        }}
                      >
                        <Eye size={14} />
                        {selectedAssignment.trainingProgram === "All Units Selected" ? "View All Units" : "View All Documents"}
                      </Button>
                    </div>
                  ) : (
                    <div>
                      <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                        <span className="h-2 w-2 rounded-full bg-primary/70"></span>
                        Selected Training Paths
                      </h4>
                      <div className="max-h-[300px] overflow-y-auto">
                        {(() => {
                          // Extract units from the program string
                          const programParts = selectedAssignment.trainingProgram.split(' | ').map(program => {
                            // Both Training and CFD formats now use the same format: "Area -> Topic -> Unit"
                            return program.split(' -> ');
                          });

                          // Create selectedTopics or selectedCfdUnits based on assignment type
                          if (selectedAssignment.trainingProgram.includes("Checklist") || selectedAssignment.trainingProgram === "All Documents Selected") {
                            // For CFD
                            const selectedCfdUnits = programParts.map(parts => ({
                              area: parts[0],
                              topic: parts[1],
                              unit: parts[2]
                            }));

                            return (
                              <SelectedUnitsWithHierarchy
                                selectedUnits={[]}
                                areas={cfdAreas}
                                topics={cfdTopics}
                                units={cfdUnits}
                                assignmentType="CFD"
                                selectedCfdUnits={selectedCfdUnits}
                              />
                            );
                          } else {
                            // For Training
                            const selectedTopics = programParts.map(parts => ({
                              area: parts[0],
                              topic: parts[1]
                            }));

                            return (
                              <SelectedUnitsWithHierarchy
                                selectedUnits={[]}
                                areas={trainingAreas}
                                topics={trainingTopics}
                                units={trainingUnits}
                                assignmentType="Training"
                                selectedTopics={selectedTopics}
                              />
                            );
                          }
                        })()}
                      </div>
                    </div>
                  )}

                  {/* Assigned Users Hierarchical View */}
                  <div className="mt-6 pt-4 border-t border-muted/20">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-sm flex items-center gap-2">
                        <span className="h-2 w-2 rounded-full bg-primary/70"></span>
                        Assigned Users
                      </h4>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-7 text-xs"
                          >
                            <Users className="h-3.5 w-3.5 mr-1" />
                            View All Users
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-[600px] border-muted/40 shadow-lg">
                          <DialogHeader className="bg-muted/10 pb-2">
                            <DialogTitle className="text-xl font-semibold">All Users</DialogTitle>
                            <DialogDescription className="text-muted-foreground">
                              Users assigned to this training program.
                            </DialogDescription>
                          </DialogHeader>
                          <div className="py-4">
                            <div className="rounded-lg border overflow-hidden">
                              <Table>
                                <TableHeader className="bg-muted/30">
                                  <TableRow>
                                    <TableHead>Name</TableHead>
                                    <TableHead>Employee ID</TableHead>
                                    <TableHead>Department</TableHead>
                                    <TableHead>Designation</TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  <TableRow>
                                    <TableCell className="font-medium">Alex Johnson</TableCell>
                                    <TableCell>EMP-001</TableCell>
                                    <TableCell>Engineering</TableCell>
                                    <TableCell>
                                      <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full">
                                        Senior Technician
                                      </span>
                                    </TableCell>
                                  </TableRow>
                                  <TableRow>
                                    <TableCell className="font-medium">Jamie Smith</TableCell>
                                    <TableCell>EMP-002</TableCell>
                                    <TableCell>Finance</TableCell>
                                    <TableCell>
                                      <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full">
                                        Department Manager
                                      </span>
                                    </TableCell>
                                  </TableRow>
                                  <TableRow>
                                    <TableCell className="font-medium">Taylor Brown</TableCell>
                                    <TableCell>EMP-003</TableCell>
                                    <TableCell>Product</TableCell>
                                    <TableCell>
                                      <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full">
                                        Technical Lead
                                      </span>
                                    </TableCell>
                                  </TableRow>
                                  <TableRow>
                                    <TableCell className="font-medium">Robin Lee</TableCell>
                                    <TableCell>EXT-001</TableCell>
                                    <TableCell>Research</TableCell>
                                    <TableCell>
                                      <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full">
                                        Specialist
                                      </span>
                                    </TableCell>
                                  </TableRow>
                                  <TableRow>
                                    <TableCell className="font-medium">Sam Green</TableCell>
                                    <TableCell>EXT-002</TableCell>
                                    <TableCell>Training</TableCell>
                                    <TableCell>
                                      <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full">
                                        Administrative Coordinator
                                      </span>
                                    </TableCell>
                                  </TableRow>
                                </TableBody>
                              </Table>
                            </div>
                          </div>
                          <DialogFooter className="border-t border-muted/20 pt-4">
                            <DialogClose asChild>
                              <Button type="button" variant="secondary" className="transition-all duration-200 hover:bg-muted/80 hover:border-primary/30 shadow-sm">
                                Close
                              </Button>
                            </DialogClose>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>

                    {selectedAssignment ? (
                      <div className="p-4 bg-muted/10 rounded-md border border-muted/20">
                        {selectedAssignment.employeeIds && selectedAssignment.employeeIds.length > 0 ? (
                          <>
                            <p className="text-sm text-muted-foreground mb-2">
                              {selectedAssignment.employeeIds.length} employee(s) assigned to this training program:
                            </p>
                            <div className="max-h-[100px] overflow-y-auto">
                              {selectedAssignment.employeeIds.map((employeeId) => {
                                const employee = users.find(user => user.id === employeeId);
                                return employee ? (
                                  <div key={employeeId} className="text-sm py-1 border-b border-muted/10 last:border-0">
                                    {employee.name} ({employee.employeeNo})
                                  </div>
                                ) : null;
                              })}
                            </div>
                          </>
                        ) : selectedAssignment.employeeName ? (
                          <p className="text-sm">
                            Assigned to: <span className="font-medium">{selectedAssignment.employeeName}</span>
                          </p>
                        ) : (
                          <p className="text-sm text-muted-foreground text-center">No employee information available</p>
                        )}
                      </div>
                    ) : (
                      <div className="p-4 bg-muted/10 rounded-md border border-muted/20 text-center">
                        <p className="text-sm text-muted-foreground">Click "View All Users" to see users assigned to this training program.</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        <DialogFooter className="border-t border-muted/20 pt-4 mt-auto">
          <div className="flex gap-2 w-full justify-between">
            <div className="flex gap-2 flex-wrap">
              <Button
                type="button"
                variant="outline"
                className="transition-all duration-200 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 shadow-sm"
                onClick={() => {
                  setAssignmentToEdit(selectedAssignment);
                  setEditDialogOpen(true);
                  setDetailsDialogOpen(false);
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                  <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                  <path d="m15 5 4 4"></path>
                </svg>
                Edit Assignment
              </Button>
              <Button
                type="button"
                variant="outline"
                className="transition-all duration-200 hover:bg-green-50 hover:text-green-600 hover:border-green-200 shadow-sm"
                onClick={() => {
                  if (selectedAssignment) {
                    try {
                      let extractedUnits: string[] = [];
                      let unitHierarchy: { area: string; topic: string; unit: string }[] = [];

                      if (selectedAssignment.trainingProgram === "All Units Selected" || selectedAssignment.trainingProgram === "All Documents Selected") {
                        // If all units/documents are selected, get all units from all areas and topics
                        const currentAreas = selectedAssignment.trainingProgram === "All Documents Selected" ? cfdAreas : trainingAreas;
                        const currentTopics = selectedAssignment.trainingProgram === "All Documents Selected" ? cfdTopics : trainingTopics;
                        const currentUnits = selectedAssignment.trainingProgram === "All Documents Selected" ? cfdUnits : trainingUnits;

                        currentAreas.forEach((area: string) => {
                          const areaTopics = currentTopics[area as keyof typeof currentTopics] || [];
                          (areaTopics as string[]).forEach((topic: string) => {
                            const topicUnits = currentUnits[topic as keyof typeof currentUnits] || [];
                            (topicUnits as string[]).forEach((unit: string) => {
                              extractedUnits.push(unit);
                              unitHierarchy.push({
                                area,
                                topic,
                                unit
                              });
                            });
                          });
                        });
                      } else {
                        // Extract units from the program string
                        const programParts = selectedAssignment.trainingProgram.split(' | ').map(program => {
                          // Both Training and CFD formats now use the same format: "Area -> Topic -> Unit"
                          return program.split(' -> ');
                        });
                        extractedUnits = programParts.map(parts => parts[2]);

                        // Create unit hierarchy data
                        unitHierarchy = programParts.map(parts => ({
                          area: parts[0],
                          topic: parts[1],
                          unit: parts[2]
                        }));
                      }

                      // Generate and download PDF
                      generateTrainingAssignmentPDF(
                        selectedAssignment,
                        users, // Using the mock users data
                        extractedUnits,
                        unitHierarchy
                      );

                      toast({
                        title: "PDF Generated",
                        description: "Training assignment details have been downloaded as a PDF.",
                      });
                    } catch (error) {
                      console.error("Error generating PDF:", error);
                      toast({
                        title: "Error",
                        description: "There was an error generating the PDF. Please try again.",
                        variant: "destructive",
                      });
                    }
                  }
                }}
              >
                <FileDown className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
            </div>
            <DialogClose asChild>
              <Button type="button" variant="secondary" className="transition-all duration-200 hover:bg-muted/80 hover:border-primary/30 shadow-sm">
                Close
              </Button>
            </DialogClose>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* Edit Assignment Dialog */}
    <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
      <DialogContent className="sm:max-w-[700px] border-muted/40 shadow-lg">
        <DialogHeader className="bg-muted/10 pb-2">
          <DialogTitle className="text-xl font-semibold">Edit Training Assignment</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            Update the details of this training assignment.
          </DialogDescription>
        </DialogHeader>

        {assignmentToEdit && (
          <div className="py-4">
            <div className="grid gap-4">
              <div>
                <Label htmlFor="edit-status">Status</Label>
                <Select
                  value={assignmentToEdit.status}
                  onValueChange={(value) => {
                    setAssignmentToEdit({...assignmentToEdit, status: value});
                  }}
                >
                  <SelectTrigger id="edit-status">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statuses.map((status) => (
                      <SelectItem key={status} value={status}>{status}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="edit-notes">Notes</Label>
                <textarea
                  id="edit-notes"
                  className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={assignmentToEdit.notes || ''}
                  onChange={(e) => setAssignmentToEdit({...assignmentToEdit, notes: e.target.value})}
                  placeholder="Add notes about this assignment..."
                />
              </div>
            </div>
          </div>
        )}

        <DialogFooter className="border-t border-muted/20 pt-4">
          <div className="flex gap-2 w-full justify-end">
            <DialogClose asChild>
              <Button type="button" variant="outline" className="transition-all duration-200 hover:bg-muted/80 hover:border-primary/30 shadow-sm">
                Cancel
              </Button>
            </DialogClose>
            <Button
              type="button"
              className="transition-all duration-200 hover:bg-primary/90 shadow-sm"
              onClick={() => {
                if (assignmentToEdit) {
                  // Update the assignment in the array
                  setTrainingAssignments(trainingAssignments.map(a =>
                    a.id === assignmentToEdit.id ? assignmentToEdit : a
                  ));

                  const employeeText = assignmentToEdit.employeeIds
                    ? (assignmentToEdit.employeeIds.length > 1 ? `${assignmentToEdit.employeeIds.length} employees` : "1 employee")
                    : (assignmentToEdit.employeeName || "the employee");

                  toast({
                    title: "Assignment Updated",
                    description: `Training assignment for ${employeeText} has been updated.`,
                  });

                  setEditDialogOpen(false);
                }
              }}
            >
              Save Changes
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
    </>
  );
}

